.TH IBPRINTSWITCH 8 "May 31, 2007" "OpenIB" "OpenIB Diagnostics"

.SH NAME
ibprintswitch.pl \- print either the switch specified or a list of switches
from the ibnetdiscover output

.SH SYNOPSIS
.B ibprintswitch.pl
[-R -l -C <ca_name> -P <ca_port>] [<switch_guid|switch_name>]

.SH DESCRIPTION
.PP
Faster than greping/viewing with an editor the output of ibnetdiscover,
ibprintswitch.pl will parse out and print either the switch information
for the switch specified or a list of all the switches found in the subnet.
In addition, it will crudely parse on the node description
information and if found report all the information for an entire chasis
if the description information is consistent.

Finally, ibprintswitch.pl will also reuse the cached ibnetdiscover output
from some of the other diag tools which makes it a bit faster than running
ibnetdiscover from scratch.

.SH OPTIONS

.PP
.TP
\fB\-l\fR
List the switches (simply a wrapper for ibswitches).
.TP
\fB\-R\fR
Recalculate the ibnetdiscover information, ie do not use the cached
information.  This option is slower but should be used if the diag tools have
not been used for some time or if there are other reasons to believe that
the fabric has changed.
.TP
\fB\-C <ca_name>\fR    use the specified ca_name for the search.
.TP
\fB\-P <ca_port>\fR    use the specified ca_port for the search.


.SH AUTHORS
.TP
Ira Weiny
.RI < <EMAIL> >
.TP
Hal Rosenstock
.RI < <EMAIL> >
