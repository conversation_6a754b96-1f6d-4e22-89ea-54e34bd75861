.\" Man page generated from reStructuredText.
.
.TH CHECK_LFT_BALANCE 8 "@BUILD_DATE@" "" "Open IB Diagnostics"
.SH NAME
check_lft_balance \- check InfiniBand unicast forwarding tables balance
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
check_lft_balance.sh [\-hRv]
.SH DESCRIPTION
.sp
check_lft_balance.sh is a script which checks for balancing in Infiniband
unicast forwarding tables.  It analyzes the output of
\fBdump_lfts(8)\fP and \fBiblinkinfo(8)\fP
.SH OPTIONS
.INDENT 0.0
.TP
.B \fB\-h\fP
show help
.TP
.B \fB\-R\fP
Recalculate dump_lfts information, ie do not use the cached
information.  This option is slower but should be used if the diag
tools have not been used for some time or if there are other reasons to
believe that the fabric has changed.
.TP
.B \fB\-v\fP
verbose output
.UNINDENT
.SH SEE ALSO
.sp
\fBdump_lfts(8)\fP
\fBiblinkinfo(8)\fP
.SH AUTHORS
.INDENT 0.0
.TP
.B Albert Chu
< \fI\%<EMAIL>\fP >
.UNINDENT
.\" Generated by docutils manpage writer.
.
