Modular Routine Engine

Modular routing engine structure has been added to allow
for ease of "plugging" new routing modules.

Currently, only unicast callbacks are supported. Multicast
can be added later.

One of existing routing modules is up-down "updn", which may
be activated with '-R updn' option (instead of old '-u').

General usage is:
$ opensm -R 'module-name'

There is also a trivial routing module which is able
to load LFT tables from a file.

Main features:

- this will load switch LFTs and/or LID matrices (min hops tables)
- this will load switch LFTs according to the path entries introduced in
  the file
- no additional checks will be performed (such as "is port connected", etc.)
- in case when fabric LIDs were changed this will try to reconstruct LFTs
  correctly if endport GUIDs are represented in the file (in order
  to disable this GUIDs may be removed from the file or zeroed)

The file format is compatible with output of 'ibroute' util and for
whole fabric may be generated with script like this:

  for sw_lid in `ibswitches | awk '{print $NF}'` ; do
	ibroute $sw_lid
  done > /path/to/lfts_file

, or using DR paths:

  for sw_dr in `ibnetdiscover -v \
		| sed -ne '/^DR path .* switch /s/^DR path \[\(.*\)\].*$/\1/p' \
		| sed -e 's/\]\[/,/g' \
		| sort -u` ; do
	ibroute -D ${sw_dr}
  done > /path/to/lfts_file

This script is dump_lfts.sh

In order to activate new module use:

  opensm -R file -U /path/to/lfts_file

If the lfts_file is not found or is in error, the default routing
algorithm is utilized.

The ability to dump switch lid matrices (aka min hops tables) to file and
later to load these is also supported.

The usage is similar to unicast forwarding tables loading from a lfts
file (introduced by 'file' routing engine), but new lid matrix file
name should be specified by -M or --lid_matrix_file option. For example:

  opensm -R file -M ./opensm-lid-matrix.dump

The dump file is named 'opensm-lid-matrix.dump' and will be generated in
standard opensm dump directory (/var/log by default) when
OSM_LOG_ROUTING logging flag is set.

When routing engine 'file' is activated, but the lfts file is not specified
or cannot be opened, the default lid matrix algorithm will be used.

There is also a switch forwarding tables dumper which generates
a file compatible with dump_lfts.sh output. This file can be used
as input for forwarding tables loading by 'file' routing engine.
Both or one of options -U and -M can be specified together with '-R file'.

NOTE: ibroute has been updated (for switch management ports) to support this.
Also, lmc was added to switch management ports. ibroute needs to be r7855 or
later from the trunk.

