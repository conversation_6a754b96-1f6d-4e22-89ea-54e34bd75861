.\" Man page generated from reStructuredText.
.
.TH SAQUERY 8 "@BUILD_DATE@" "" "Open IB Diagnostics"
.SH NAME
saquery \- query InfiniBand subnet administration attributes
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
saquery [options] [<name> | <lid> | <guid>]
.SH DESCRIPTION
.sp
saquery issues the selected SA query. Node records are queried by default.
.SH OPTIONS
.INDENT 0.0
.TP
.B \fB\-p\fP
get PathRecord info
.TP
.B \fB\-N\fP
get NodeRecord info
.TP
.B \fB\-D, \-\-list\fP
get NodeDescriptions of CAs only
.TP
.B \fB\-S\fP
get ServiceRecord info
.TP
.B \fB\-I\fP
get InformInfoRecord (subscription) info
.TP
.B \fB\-L\fP
return the Lids of the name specified
.TP
.B \fB\-l\fP
return the unique Lid of the name specified
.TP
.B \fB\-G\fP
return the Guids of the name specified
.TP
.B \fB\-O\fP
return the name for the Lid specified
.TP
.B \fB\-U\fP
return the name for the Guid specified
.TP
.B \fB\-c\fP
get the SA\(aqs class port info
.TP
.B \fB\-s\fP
return the PortInfoRecords with isSM or isSMdisabled capability mask bit on
.TP
.B \fB\-g\fP
get multicast group info
.TP
.B \fB\-m\fP
get multicast member info.  If a group is specified, limit the output
to the group specified and print one line containing only the GUID and
node description for each entry. Example: saquery \-m 0xc000
.TP
.B \fB\-x\fP
get LinkRecord info
.TP
.B \fB\-\-src\-to\-dst <src:dst>\fP
get a PathRecord for <src:dst>
where src and dst are either node names or LIDs
.TP
.B \fB\-\-sgid\-to\-dgid <sgid:dgid>\fP
get a PathRecord for \fBsgid\fP to \fBdgid\fP
where both GIDs are in an IPv6 format acceptable to \fBinet_pton (3)\fP
.TP
.B \fB\-\-smkey <val>\fP
use SM_Key value for the query. Will be used only with "trusted"
queries.  If non\-numeric value (like \(aqx\(aq) is specified then saquery
will prompt for a value.
Default (when not specified here or in
@IBDIAG_CONFIG_PATH@/ibdiag.conf) is to use SM_Key == 0 (or
"untrusted")
.UNINDENT
.\" Define the common option -K
.
.INDENT 0.0
.TP
.B \fB\-K, \-\-show_keys\fP
show security keys (mkey, smkey, etc.) associated with the request.
.UNINDENT
.sp
\fB\-\-slid <lid>\fP Source LID (PathRecord)
.sp
\fB\-\-dlid <lid>\fP Destination LID (PathRecord)
.sp
\fB\-\-mlid <lid>\fP Multicast LID (MCMemberRecord)
.sp
\fB\-\-sgid <gid>\fP Source GID (IPv6 format) (PathRecord)
.sp
\fB\-\-dgid <gid>\fP Destination GID (IPv6 format) (PathRecord)
.sp
\fB\-\-gid <gid>\fP Port GID (MCMemberRecord)
.sp
\fB\-\-mgid <gid>\fP Multicast GID (MCMemberRecord)
.sp
\fB\-\-reversible\fP Reversible path (PathRecord)
.sp
\fB\-\-numb_path\fP Number of paths (PathRecord)
.INDENT 0.0
.TP
.B \fB\-\-pkey\fP P_Key (PathRecord, MCMemberRecord). If non\-numeric value (like \(aqx\(aq)
is specified then saquery will prompt for a value
.UNINDENT
.sp
\fB\-\-qos_class\fP QoS Class (PathRecord)
.sp
\fB\-\-sl\fP Service level (PathRecord, MCMemberRecord)
.sp
\fB\-\-mtu\fP MTU and selector (PathRecord, MCMemberRecord)
.sp
\fB\-\-rate\fP Rate and selector (PathRecord, MCMemberRecord)
.sp
\fB\-\-pkt_lifetime\fP Packet lifetime and selector (PathRecord, MCMemberRecord)
.INDENT 0.0
.TP
.B \fB\-\-qkey\fP Q_Key (MCMemberRecord). If non\-numeric value (like \(aqx\(aq) is specified
then saquery will prompt for a value
.UNINDENT
.sp
\fB\-\-tclass\fP Traffic Class (PathRecord, MCMemberRecord)
.sp
\fB\-\-flow_label\fP Flow Label (PathRecord, MCMemberRecord)
.sp
\fB\-\-hop_limit\fP Hop limit (PathRecord, MCMemberRecord)
.sp
\fB\-\-scope\fP Scope (MCMemberRecord)
.sp
\fB\-\-join_state\fP Join state (MCMemberRecord)
.sp
\fB\-\-proxy_join\fP Proxy join (MCMemberRecord)
.sp
\fB\-\-service_id\fP ServiceID (PathRecord)
.sp
Supported query names (and aliases):
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
ClassPortInfo (CPI)
NodeRecord (NR) [lid]
PortInfoRecord (PIR) [[lid]/[port]/[options]]
SL2VLTableRecord (SL2VL) [[lid]/[in_port]/[out_port]]
PKeyTableRecord (PKTR) [[lid]/[port]/[block]]
VLArbitrationTableRecord (VLAR) [[lid]/[port]/[block]]
InformInfoRecord (IIR)
LinkRecord (LR) [[from_lid]/[from_port]] [[to_lid]/[to_port]]
ServiceRecord (SR)
PathRecord (PR)
MCMemberRecord (MCMR)
LFTRecord (LFTR) [[lid]/[block]]
MFTRecord (MFTR) [[mlid]/[position]/[block]]
GUIDInfoRecord (GIR) [[lid]/[block]]
SwitchInfoRecord (SWIR) [lid]
SMInfoRecord (SMIR) [lid]
.ft P
.fi
.UNINDENT
.UNINDENT
.SS Port Selection flags
.\" Define the common option -C
.
.sp
\fB\-C, \-\-Ca <ca_name>\fP    use the specified ca_name.
.\" Define the common option -P
.
.sp
\fB\-P, \-\-Port <ca_port>\fP    use the specified ca_port.
.\" Explanation of local port selection
.
.SS Local port Selection
.sp
Multiple port/Multiple CA support: when no IB device or port is specified
(see the "local umad parameters" below), the libibumad library
selects the port to use by the following criteria:
.INDENT 0.0
.INDENT 3.5
.INDENT 0.0
.IP 1. 3
the first port that is ACTIVE.
.IP 2. 3
if not found, the first port that is UP (physical link up).
.UNINDENT
.sp
If a port and/or CA name is specified, the libibumad library attempts
to fulfill the user request, and will fail if it is not possible.
.sp
For example:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
ibaddr                 # use the first port (criteria #1 above)
ibaddr \-C mthca1       # pick the best port from "mthca1" only.
ibaddr \-P 2            # use the second (active/up) port from the first available IB device.
ibaddr \-C mthca0 \-P 2  # use the specified port only.
.ft P
.fi
.UNINDENT
.UNINDENT
.UNINDENT
.UNINDENT
.SS Debugging flags
.\" Define the common option -d
.
.INDENT 0.0
.TP
.B \-d
raise the IB debugging level.
May be used several times (\-ddd or \-d \-d \-d).
.UNINDENT
.\" Define the common option -e
.
.INDENT 0.0
.TP
.B \-e
show send and receive errors (timeouts and others)
.UNINDENT
.\" Define the common option -h
.
.sp
\fB\-h, \-\-help\fP      show the usage message
.\" Define the common option -v
.
.INDENT 0.0
.TP
.B \fB\-v, \-\-verbose\fP
increase the application verbosity level.
May be used several times (\-vv or \-v \-v \-v)
.UNINDENT
.\" Define the common option -V
.
.sp
\fB\-V, \-\-version\fP     show the version info.
.SS Configuration flags
.\" Define the common option -t
.
.sp
\fB\-t, \-\-timeout <timeout_ms>\fP override the default timeout for the solicited mads.
.\" Define the common option -z
.
.INDENT 0.0
.TP
.B \fB\-\-outstanding_smps, \-o <val>\fP
Specify the number of outstanding SMP\(aqs which should be issued during the scan
.sp
Default: 2
.UNINDENT
.\" Define the common option --node-name-map
.
.sp
\fB\-\-node\-name\-map <node\-name\-map>\fP Specify a node name map.
.INDENT 0.0
.INDENT 3.5
This file maps GUIDs to more user friendly names.  See FILES section.
.UNINDENT
.UNINDENT
.\" Define the common option -z
.
.sp
\fB\-\-config, \-z  <config_file>\fP Specify alternate config file.
.INDENT 0.0
.INDENT 3.5
Default: @IBDIAG_CONFIG_PATH@/ibdiag.conf
.UNINDENT
.UNINDENT
.SH COMMON FILES
.\" Common text for the config file
.
.SS CONFIG FILE
.sp
@IBDIAG_CONFIG_PATH@/ibdiag.conf
.sp
A global config file is provided to set some of the common options for all
tools.  See supplied config file for details.
.\" Common text to describe the node name map file.
.
.SS NODE NAME MAP FILE FORMAT
.sp
The node name map is used to specify user friendly names for nodes in the
output.  GUIDs are used to perform the lookup.
.sp
This functionality is provided by the opensm\-libs package.  See \fBopensm(8)\fP
for the file location for your installation.
.sp
\fBGenerically:\fP
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
# comment
<guid> "<name>"
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
\fBExample:\fP
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
# IB1
# Line cards
0x0008f104003f125c "IB1 (Rack 11 slot 1   ) ISR9288/ISR9096 Voltaire sLB\-24D"
0x0008f104003f125d "IB1 (Rack 11 slot 1   ) ISR9288/ISR9096 Voltaire sLB\-24D"
0x0008f104003f10d2 "IB1 (Rack 11 slot 2   ) ISR9288/ISR9096 Voltaire sLB\-24D"
0x0008f104003f10d3 "IB1 (Rack 11 slot 2   ) ISR9288/ISR9096 Voltaire sLB\-24D"
0x0008f104003f10bf "IB1 (Rack 11 slot 12  ) ISR9288/ISR9096 Voltaire sLB\-24D"

# Spines
0x0008f10400400e2d "IB1 (Rack 11 spine 1   ) ISR9288 Voltaire sFB\-12D"
0x0008f10400400e2e "IB1 (Rack 11 spine 1   ) ISR9288 Voltaire sFB\-12D"
0x0008f10400400e2f "IB1 (Rack 11 spine 1   ) ISR9288 Voltaire sFB\-12D"
0x0008f10400400e31 "IB1 (Rack 11 spine 2   ) ISR9288 Voltaire sFB\-12D"
0x0008f10400400e32 "IB1 (Rack 11 spine 2   ) ISR9288 Voltaire sFB\-12D"

# GUID   Node Name
0x0008f10400411a08 "SW1  (Rack  3) ISR9024 Voltaire 9024D"
0x0008f10400411a28 "SW2  (Rack  3) ISR9024 Voltaire 9024D"
0x0008f10400411a34 "SW3  (Rack  3) ISR9024 Voltaire 9024D"
0x0008f104004119d0 "SW4  (Rack  3) ISR9024 Voltaire 9024D"
.ft P
.fi
.UNINDENT
.UNINDENT
.SH DEPENDENCIES
.sp
OpenSM (or other running SM/SA), libosmcomp, libibumad, libibmad
.SH AUTHORS
.INDENT 0.0
.TP
.B Ira Weiny
< \fI\%<EMAIL>\fP >
.TP
.B Hal Rosenstock
< \fI\%<EMAIL>\fP >
.UNINDENT
.\" Generated by docutils manpage writer.
.
