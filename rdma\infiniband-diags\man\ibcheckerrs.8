.TH IBCHECKERRS 8 "May 30, 2007" "OpenIB" "OpenIB Diagnostics"

.SH NAME
ibcheckerrs \- validate IB port (or node) and report errors in counters above threshold

.SH SYNOPSIS
.B ibcheckerrs
[\-h] [\-b] [\-v] [\-G] [\-T <threshold_file>] [\-s(how_thresholds)]
[\-N | \-nocolor] [\-C ca_name] [\-P ca_port] [\-t(imeout) timeout_ms]
<lid|guid> <port>


.SH DESCRIPTION
.PP
Check specified port (or node) and report errors that surpassed their predefined
threshold. Port address is lid unless -G option is used to specify a GUID
address. The predefined thresholds can be dumped using the -s option, and a
user defined threshold_file (using the same format as the dump) can be
specified using the -t <file> option.

.SH OPTIONS
.PP
\-G      use GUID address argument. In most cases, it is the Port GUID.
        Example:
        "0x08f1040023"
.PP
\-s      show predefined thresholds
.PP
\-T      use specified threshold file
.PP
\-v      increase the verbosity level
.PP
\-b      brief mode. Reduce the output to show only if errors are
        present, not what they are.
.PP
\-N | \-nocolor use mono rather than color mode
.PP
\-C <ca_name>    use the specified ca_name.
.PP
\-P <ca_port>    use the specified ca_port.
.PP
\-t <timeout_ms> override the default timeout for the solicited mads.

.SH EXAMPLE
.PP
ibcheckerrs 2           # check aggregated node counter for lid 2
.PP
ibcheckerrs 2   4       # check port counters for lid 2 port 4
.PP
ibcheckerrs -T xxx 2    # check node using xxx threshold file

.SH SEE ALSO
.BR perfquery(8),
.BR ibaddr(8)

.SH AUTHOR
.TP
Hal Rosenstock
.RI < <EMAIL> >
