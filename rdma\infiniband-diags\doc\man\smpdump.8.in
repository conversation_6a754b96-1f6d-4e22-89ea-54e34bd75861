.\" Man page generated from reStructuredText.
.
.TH SMPDUMP 8 "@BUILD_DATE@" "" "Open IB Diagnostics"
.SH NAME
smpdump \- dump InfiniBand subnet management attributes
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
smpdump [options] <dlid|dr_path> <attribute> [attribute_modifier]
.SH DESCRIPTION
.sp
smpdump is a general purpose SMP utility which gets SM attributes from a
specified SMA. The result is dumped in hex by default.
.SH OPTIONS
.INDENT 0.0
.TP
.B \fBdlid|drpath\fP
LID or DR path to SMA
.TP
.B \fBattribute\fP
IBA attribute ID for SM attribute
.TP
.B \fBattribute_modifier\fP
IBA modifier for SM attribute
.TP
.B \fB\-s, \-\-string\fP
Print strings in packet if possible
.UNINDENT
.SS Addressing Flags
.\" Define the common option -D for Directed routes
.
.sp
\fB\-D, \-\-Direct\fP     The address specified is a directed route
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
Examples:
   [options] \-D [options] "0"          # self port
   [options] \-D [options] "0,1,2,1,4"  # out via port 1, then 2, ...

   (Note the second number in the path specified must match the port being
   used.  This can be specified using the port selection flag \(aq\-P\(aq or the
   port found through the automatic selection process.)
.ft P
.fi
.UNINDENT
.UNINDENT
.\" Define the common option -L
.
.sp
\fB\-L, \-\-Lid\fP   The address specified is a LID
.SS Port Selection flags
.\" Define the common option -C
.
.sp
\fB\-C, \-\-Ca <ca_name>\fP    use the specified ca_name.
.\" Define the common option -P
.
.sp
\fB\-P, \-\-Port <ca_port>\fP    use the specified ca_port.
.\" Explanation of local port selection
.
.SS Local port Selection
.sp
Multiple port/Multiple CA support: when no IB device or port is specified
(see the "local umad parameters" below), the libibumad library
selects the port to use by the following criteria:
.INDENT 0.0
.INDENT 3.5
.INDENT 0.0
.IP 1. 3
the first port that is ACTIVE.
.IP 2. 3
if not found, the first port that is UP (physical link up).
.UNINDENT
.sp
If a port and/or CA name is specified, the libibumad library attempts
to fulfill the user request, and will fail if it is not possible.
.sp
For example:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
ibaddr                 # use the first port (criteria #1 above)
ibaddr \-C mthca1       # pick the best port from "mthca1" only.
ibaddr \-P 2            # use the second (active/up) port from the first available IB device.
ibaddr \-C mthca0 \-P 2  # use the specified port only.
.ft P
.fi
.UNINDENT
.UNINDENT
.UNINDENT
.UNINDENT
.SS Debugging flags
.\" Define the common option -d
.
.INDENT 0.0
.TP
.B \-d
raise the IB debugging level.
May be used several times (\-ddd or \-d \-d \-d).
.UNINDENT
.\" Define the common option -e
.
.INDENT 0.0
.TP
.B \-e
show send and receive errors (timeouts and others)
.UNINDENT
.\" Define the common option -h
.
.sp
\fB\-h, \-\-help\fP      show the usage message
.\" Define the common option -v
.
.INDENT 0.0
.TP
.B \fB\-v, \-\-verbose\fP
increase the application verbosity level.
May be used several times (\-vv or \-v \-v \-v)
.UNINDENT
.\" Define the common option -V
.
.sp
\fB\-V, \-\-version\fP     show the version info.
.SS Configuration flags
.\" Define the common option -t
.
.sp
\fB\-t, \-\-timeout <timeout_ms>\fP override the default timeout for the solicited mads.
.\" Define the common option -z
.
.sp
\fB\-\-config, \-z  <config_file>\fP Specify alternate config file.
.INDENT 0.0
.INDENT 3.5
Default: @IBDIAG_CONFIG_PATH@/ibdiag.conf
.UNINDENT
.UNINDENT
.SH FILES
.\" Common text for the config file
.
.SS CONFIG FILE
.sp
@IBDIAG_CONFIG_PATH@/ibdiag.conf
.sp
A global config file is provided to set some of the common options for all
tools.  See supplied config file for details.
.SH EXAMPLES
.sp
Direct Routed Examples
.INDENT 0.0
.TP
.B ::
smpdump \-D 0,1,2,3,5 16 # NODE DESC
smpdump \-D 0,1,2 0x15 2 # PORT INFO, port 2
.UNINDENT
.sp
LID Routed Examples
.INDENT 0.0
.TP
.B ::
smpdump 3 0x15 2        # PORT INFO, lid 3 port 2
smpdump 0xa0 0x11       # NODE INFO, lid 0xa0
.UNINDENT
.SH SEE ALSO
.sp
smpquery (8)
.SH AUTHOR
.INDENT 0.0
.TP
.B Hal Rosenstock
< \fI\%<EMAIL>\fP >
.UNINDENT
.\" Generated by docutils manpage writer.
.
