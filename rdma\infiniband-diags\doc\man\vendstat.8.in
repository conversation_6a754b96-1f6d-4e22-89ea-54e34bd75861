.\" Man page generated from reStructuredText.
.
.TH VENDSTAT 8 "@BUILD_DATE@" "" "Open IB Diagnostics"
.SH NAME
vendstat \- query InfiniBand vendor specific functions
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
vendstat [options] <lid|guid>
.SH DESCRIPTION
.sp
vendstat uses vendor specific MADs to access beyond the IB spec
vendor specific functionality. Currently, there is support for
Mellanox InfiniSwitch\-III (IS3) and InfiniSwitch\-IV (IS4).
.SH OPTIONS
.INDENT 0.0
.TP
.B \fB\-N\fP
show IS3 or IS4 general information.
.TP
.B \fB\-w\fP
show IS3 port xmit wait counters.
.TP
.B \fB\-i\fP
show IS4 counter group info.
.TP
.B \fB\-c <num,num>\fP
configure IS4 counter groups.
.sp
Configure IS4 counter groups 0 and 1. Such configuration is not
persistent across IS4 reboot.  First number is for counter group 0 and
second is for counter group 1.
.sp
Group 0 counter config values:
.UNINDENT
.INDENT 0.0
.TP
.B ::
.INDENT 7.0
.INDENT 3.5
0 \- PortXmitDataSL0\-7
1 \- PortXmitDataSL8\-15
2 \- PortRcvDataSL0\-7
.UNINDENT
.UNINDENT
.sp
Group 1 counter config values:
.UNINDENT
.INDENT 0.0
.TP
.B ::
1 \- PortXmitDataSL8\-15
2 \- PortRcvDataSL0\-7
8 \- PortRcvDataSL8\-15
.TP
.B \fB\-R, \-\-Read <addr,mask>\fP
Read configuration space record at addr
.TP
.B \fB\-W, \-\-Write <addr,val,mask>\fP
Write configuration space record at addr
.UNINDENT
.SS Addressing Flags
.\" Define the common option -G
.
.sp
\fB\-G, \-\-Guid\fP     The address specified is a Port GUID
.\" Define the common option -L
.
.sp
\fB\-L, \-\-Lid\fP   The address specified is a LID
.\" Define the common option -s
.
.sp
\fB\-s, \-\-sm_port <smlid>\fP     use \(aqsmlid\(aq as the target lid for SA queries.
.SS Port Selection flags
.\" Define the common option -C
.
.sp
\fB\-C, \-\-Ca <ca_name>\fP    use the specified ca_name.
.\" Define the common option -P
.
.sp
\fB\-P, \-\-Port <ca_port>\fP    use the specified ca_port.
.\" Explanation of local port selection
.
.SS Local port Selection
.sp
Multiple port/Multiple CA support: when no IB device or port is specified
(see the "local umad parameters" below), the libibumad library
selects the port to use by the following criteria:
.INDENT 0.0
.INDENT 3.5
.INDENT 0.0
.IP 1. 3
the first port that is ACTIVE.
.IP 2. 3
if not found, the first port that is UP (physical link up).
.UNINDENT
.sp
If a port and/or CA name is specified, the libibumad library attempts
to fulfill the user request, and will fail if it is not possible.
.sp
For example:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
ibaddr                 # use the first port (criteria #1 above)
ibaddr \-C mthca1       # pick the best port from "mthca1" only.
ibaddr \-P 2            # use the second (active/up) port from the first available IB device.
ibaddr \-C mthca0 \-P 2  # use the specified port only.
.ft P
.fi
.UNINDENT
.UNINDENT
.UNINDENT
.UNINDENT
.SS Debugging flags
.\" Define the common option -d
.
.INDENT 0.0
.TP
.B \-d
raise the IB debugging level.
May be used several times (\-ddd or \-d \-d \-d).
.UNINDENT
.\" Define the common option -e
.
.INDENT 0.0
.TP
.B \-e
show send and receive errors (timeouts and others)
.UNINDENT
.\" Define the common option -h
.
.sp
\fB\-h, \-\-help\fP      show the usage message
.\" Define the common option -v
.
.INDENT 0.0
.TP
.B \fB\-v, \-\-verbose\fP
increase the application verbosity level.
May be used several times (\-vv or \-v \-v \-v)
.UNINDENT
.\" Define the common option -V
.
.sp
\fB\-V, \-\-version\fP     show the version info.
.SS Configuration flags
.\" Define the common option -t
.
.sp
\fB\-t, \-\-timeout <timeout_ms>\fP override the default timeout for the solicited mads.
.\" Define the common option -z
.
.sp
\fB\-\-config, \-z  <config_file>\fP Specify alternate config file.
.INDENT 0.0
.INDENT 3.5
Default: @IBDIAG_CONFIG_PATH@/ibdiag.conf
.UNINDENT
.UNINDENT
.SH FILES
.\" Common text for the config file
.
.SS CONFIG FILE
.sp
@IBDIAG_CONFIG_PATH@/ibdiag.conf
.sp
A global config file is provided to set some of the common options for all
tools.  See supplied config file for details.
.SH EXAMPLES
.INDENT 0.0
.TP
.B ::
vendstat \-N 6           # read IS3 or IS4 general information
vendstat \-w 6           # read IS3 port xmit wait counters
vendstat \-i 6 12        # read IS4 port 12 counter group info
vendstat \-c 0,1 6 12    # configure IS4 port 12 counter groups for PortXmitDataSL
vendstat \-c 2,8 6 12    # configure IS4 port 12 counter groups for PortRcvDataSL
.UNINDENT
.SH AUTHOR
.INDENT 0.0
.TP
.B Hal Rosenstock
< \fI\%<EMAIL>\fP >
.UNINDENT
.\" Generated by docutils manpage writer.
.
