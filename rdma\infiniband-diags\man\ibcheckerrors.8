.TH IBCHECKERRORS 8 "May 21, 2007" "OpenIB" "OpenIB Diagnostics"

.SH NAME
ibcheckerrors \- validate IB subnet and report errors

.SH SYNOPSIS
.B ibcheckerrors
[\-h] [\-b] [\-v] [\-N | \-nocolor] [<topology-file> | \-C ca_name
\-P ca_port \-t(imeout) timeout_ms]

.SH DESCRIPTION
.PP
ibcheckerrors is a script which uses a full topology file that was created by
ibnetdiscover, scans the network to validate the connectivity and reports
errors (from port counters).

.SH OPTIONS
.PP
\-v	increase the verbosity level
.PP
\-b	brief mode. Reduce the output to show only if errors are present,
     not what they are.
.PP
\-N | \-nocolor	use mono rather than color mode
.PP
\-C <ca_name>   use the specified ca_name.
.PP
\-P <ca_port>   use the specified ca_port.
.PP
\-t <timeout_ms> override the default timeout for the solicited mads.

.SH SEE ALSO
.BR ibnetdiscover(8),
.BR ibchecknode(8),
.BR ibcheckport(8),
.BR ibcheckerrs(8)

.SH AUTHOR
.TP
Hal Rosenstock
.RI < <EMAIL> >
