2008-06-15 <PERSON> <<EMAIL>>
	* Add extra entry points for most functions to allow explicit
	  selection of HCA for MAD rpc.
	* Constify MAD port pointer for mad_rpc, mad_rpc_rmpp, and sa_rpc_call

2007-07-10  <PERSON> <<EMAIL>>

	* Release version 1.1.1.

2007-06-26  <PERSON> <<EMAIL>>

	* src/dump.c, src/fields.c, src/gs.c, src/rpc.c, src/sa.c,
	  src/smp.c: Change uint to unsigned for strict ANSI

2007-06-26  <PERSON> <<EMAIL>>

	* include/infiniband/mad.h: Change uint to unsigned
	  for strict ANSI

2007-06-18  <PERSON> <<EMAIL>>

	* include/infiniband/mad.h, src/fields.c: Change IB_PORT_MTRU_CAP_F
	  to IB_PORT_MTU_CAP_F

2007-06-05  <PERSON> <<EMAIL>>

	* include/infiniband/mad.h, src/fields.c: Add Notice DataDetails
	  fields for DataDetails, and trap 144 LID and CapabilityMask

2007-05-21  <PERSON> <<EMAIL>>

	* src/fields.c: Changed PortMulticastPkts to PortMulticastRcvPkts

2007-03-29  <PERSON>stock <<EMAIL>>

	* src/fields.c: Changed Xmt/RcvBytes to Xmt/RcvData

2007-03-29  Hal Rosenstock <<EMAIL>>

	* Release version 1.1.0.

2007-03-22  Hal Rosenstock <<EMAIL>>

	* src/mad.c: Implement GRH support in mad_build_pkt

	* include/infiniband/mad.h: In ib_portid_set, ensure
	  grh_present is 0

	* src/portid.c: In portid2str, fix endian of dispay of GID

2007-03-14  Hal Rosenstock <<EMAIL>>

	* include/infiniband/mad.h, src/fields.c: Add encode/decode
	  support for GUID0 in SM GUIDInfo attribute

2007-03-14  Dotan Barak <<EMAIL>>

	* include/infiniband/mad.h: Add GUIDInfo as SM attribute ID

2007-02-08  Ira Weiny <<EMAIL>>

	* src/portid.c: Change DR path output to be comma separated
	  decimal values like OpenSM

2007-01-25  Hal Rosenstock <<EMAIL>>

	* Release version 1.0.2.

2006-12-11  Hal Rosenstock <<EMAIL>>

	* src/(sa serv smp vendor).c: Change some debug parameters
	  to hex from decimal

2006-11-20  Sasha Khapyorsky <<EMAIL>>

	* src/rpc.c: Fix various uses of printf() style functions

2006-10-20  Hal Rosenstock <<EMAIL>>

	* include/infiniband/mad.h, src/resolve.c (ib_resolve_portid_str),
	  src/smp.c (smp_set), src/mad.c (mad_build_pkt),
	  src/portid.c (portid2str): Add and handle IB_DEST_DRSLID.

2006-10-09  Hal Rosenstock <<EMAIL>>

	* src/resolve.c (ib_resolve_portid_str): Use strtoull rather than
	  strtoll for IB_DEST_GUID

