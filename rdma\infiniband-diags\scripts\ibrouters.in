#!/bin/sh

IBPATH=${IBPATH:-@IBSCRIPTPATH@}

usage() {
	echo Usage: `basename $0` "[-h] [<topology-file> | -y mkey" \
	    "-C ca_name -P ca_port -t timeout_ms]"
	exit -1
}

topofile=""
ca_info=""
mkey="0"

while [ "$1" ]; do
	case $1 in
	-h | --help)
		usage
		;;
	-y | --m_key)
		case $2 in
		-*)
			usage
			;;
		esac
		if [ x$2 = x ] ; then
			usage
		fi
		shift
		mkey="$1"
		;;
	-P | --Port | -C | --Ca | -t | --timeout)
		case $2 in
		-*)
			usage
			;;
		esac
		if [ x$2 = x ] ; then
			usage
		fi
		ca_info="$ca_info $1 $2"
		shift
		;;
	-*)
		usage
		;;
	*)
		if [ "$topofile" ]; then
			usage
		fi
		topofile="$1"
		;;
	esac
	shift
done

if [ "$topofile" ]; then
	netcmd="cat $topofile"
else
	netcmd="$IBPATH/ibnetdiscover -y $mkey $ca_info"
fi

text="`eval $netcmd`"
rv=$?
echo "$text" | awk '
/^Rt/	{print $1 "\t: 0x" substr($3, 4, 16) " ports " $2 " "\
		substr($0, match($0, "#[ \t]*")+RLENGTH)}
/^ib/	{print $0; next}
/ibpanic:/	{print $0}
/ibwarn:/	{print $0}
/iberror:/	{print $0}
'
exit $rv
