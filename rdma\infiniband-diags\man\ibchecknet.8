.TH IBCHECKNET 8 "May 21, 2007" "OpenIB" "OpenIB Diagnostics"

.SH NAME
ibchecknet \- validate IB subnet and report errors

.SH SYNOPSIS
.B ibchecknet
[\-h] [\-N | \-nocolor] [<topology-file> | \-C ca_name \-P ca_port
\-t(imeout) timeout_ms]

.SH DESCRIPTION
.PP
ibchecknet is a script which uses a full topology file that was created
by ibnetdiscover, and scans the network to validate the connectivity and
reports errors (from port counters).

.SH OPTIONS
.PP
\-N | \-nocolor use mono rather than color mode
.PP
\-C <ca_name>    use the specified ca_name.
.PP
\-P <ca_port>    use the specified ca_port.
.PP
\-t <timeout_ms> override the default timeout for the solicited mads.

.SH SEE ALSO
.BR ibnetdiscover(8),
.BR ibchecknode(8),
.BR ibcheckport(8),
.BR ibcheckerrs(8)

.SH AUTHOR
.TP
Hal Rosenstock
.RI < <EMAIL> >
