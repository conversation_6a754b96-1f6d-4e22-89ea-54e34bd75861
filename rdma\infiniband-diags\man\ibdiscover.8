.TH IBDISCOVER.PL 8 "September 21, 2006" "OpenIB" "OpenIB Diagnostics"

.SH NAME
ibdiscover.pl \- annotate and compare InfiniBand topology

.SH SYNOPSIS
.B ibdiscover.pl

.SH DESCRIPTION
.PP
ibdiscover.pl uses a topology file create by ibnetdiscover and a discover.map
file which the network administrator creates which indicates the nodes
to be expected and a ibdiscover.topo file which is the expected connectivity
and produces a new connectivity file (discover.topo.new) and outputs
the changes to stdout. The network administrator can choose to replace
the "old" topo file with the new one or certain changes in.

The syntax of the ibdiscover.map file is:

<nodeGUID>|port|"Text for node"|<NodeDescription from ibnetdiscover format>

e.g.

8f10400410015|8|"ISR 6000"|# SW-6IB4 Voltaire port 0 lid 5

8f10403960558|2|"HCA 1"|# MT23108 InfiniHost Mellanox Technologies

The syntax of the old and new topo files (ibdiscover.topo and
ibdiscover.topo.new) are:

<LocalPort>|<LocalNodeGUID>|<RemotePort>|<RemoteNodeGUID>

e.g.

10|5442ba00003080|1|8f10400410015

These topo files are produced by the ibdiscover.pl tool.

.SH USAGE

.PP
ibnetdiscover | ibdiscover.pl

.SH SEE ALSO
.BR ibnetdiscover(8)

.SH AUTHOR
.TP
Hal Rosenstock
.RI < <EMAIL> >
