                        OpenSM Release Notes 3.3
                       =============================

Version: OpenSM 3.3.x
Repo:    git://github.com/linux-rdma/opensm
Date:    April 2019

1 Overview
----------
This document describes the contents of the OpenSM 3.3 release.
OpenSM is an InfiniBand compliant Subnet Manager and Administration,
and runs on top of OpenIB. The OpenSM version for this release
is opensm-3.3.24.

This document includes the following sections:
1 This Overview section (describing new features and software
  dependencies)
2 Known Issues And Limitations
3 Unsupported IB compliance statements
4 Bug Fixes
5 Main Verification Flows
6 Qualified Software Stacks and Devices

1.1 Major New Features

* Support for NDR link speed

* Support for HDR link speed and 2x link width
  IBTA has recently (beyond IBA 1.3) added support for HDR link speed
  and 2x link width.

* Nue routing algorithm.
  The Nue routing is a novel topology-agnostic routing approach which
  implicitly avoids deadlocks during the path calculation instead of
  solving both problems separately. Nue routing heuristically optimizes
  the load balancing, i.e., the number of routes per link, while
  enforcing deadlock-freedom without exceeding a given number of
  virtual lanes (VLs). Our Nue implementation for the InfiniBand subnet
  manager supports any number of virtual lanes.

* Support for ignoring throttled links with DFSSSP.
  Throttled links, either because they are faulty or throttled intentionally,
  aren't good for the achievable performance of an HPC system.
  During the discovery process, (DF)SSSP analyzes the theoretical maximum
  enabled link speed, which both ends of a peer link support and compares it
  to the actual link speed. If these numbers don't match, then the link is
  ignored as potential path for routes between nodes. A similar comparison
  is done for link width.

* Support for long transaction timeout for SM class transactions.
  Currently, it is only used for optimized SL2VLMappingTable operations.
  Option is long_transaction_timeout with default of 500 msec.

* Mesh Analysis for LASH routing algorithm.
  The performance of LASH can be improved by preconditioning the mesh in
  cases where there are multiple links connecting switches and also in
  cases where the switches are not cabled consistently.
  Activated with --do_mesh_analysis command line and config file option.

* Reloadable OpenSM configuration (preliminary implementation)
  It is possible now to reload OpenSM configuration parameters on the
  fly without restarting.

* Routing paths sorted balancing (for UpDown and MinHops)
  This sorts the port order in which routing paths balancing is performed
  by OpenSM. Helps to improve performance dramatically (40-50%) for most
  popular application communication patterns.
  To overwrite this behavior use --guid_routing_order_file command line
  option.

* Weighted Lid Matrices calculation (for UpDown, MinHop and DOR).
  This low level routing fine-tuning feature provides the means to
  define a weighting factor per port for customizing the least weight
  hops for the routing. Custom weights are provided using file specified
  with '--hop_weights_file' command line option.

* I/O nodes connectivity (for FatTree).
  This provides possibility to define the set of I/O nodes for the
  Fat-Tree routing algorithm. I/O nodes are non-CN nodes allowed to use
  up to N (specified using --max_reverse_hops) switches the wrong way
  around to improve connectivity. I/O nodes list is provided using file
  and --io_guid_file command line option.

* MGID to MLID compression - infrastructure for many MGIDs to single MLID
  compression. This becomes helpful when number of multicast groups
  exceeds subnet's MLID routing capability (normally 1024 groups). In such
  cases many multicast groups (MGID) can be routed using same MLID value.

* Torus-2QoS unicast routing algorithm - a DOR-based routing algorithm
  specialized for 2D/3D torus topologies. Torus-2QoS provides deadlock-free
  routing while supporting two quality of service (QoS) levels. In addition
  it is able to route around multiple failed fabric links or a single failed
  fabric switch without introducing deadlocks, and without changing path SL
  values granted before the failure.

* DNUP Unicast routing algorithm - similar to UPDN but allows routing in
  fabrics which have some CA nodes attached closer to the roots than some
  switch nodes.

* SSSP Unicast routing algorithm - SSSP unicast routing algorithm - a
  single-source-shortest-path routing algorithm, which globally balances the
  number of routes per link to optimize link utilization. This routing
  algorithm has no restrictions in terms of the underlying topology.

* DFSSSP unicast routing algorithm - a deadlock-free single-source-
  shortest-path routing, which uses the SSSP algorithm as the base to optimize
  link utilization and uses Infiniband virtual lanes (SL) to provide deadlock-
  freedom.

* SRIOV (Alias GUID) Support - In order to support virtualized environments,
  alias GUID support is added to OpenSM. This support allows an SA client to
  add and remove additional port GUIDs based on SubAdmSet/SubAdmDelete of
  GUIDInfoRecord.

* Extended speed support
  This provides support for FDR and EDR speeds.

* Congestion control support (experimental)

* Many code improvements, optimizations and cleanups.

* Windows support (early stage).

1.2 Minor New Features:

b17b4db Backward compatibility for old drivers
924f030 Add support for registering an opensm plugin as a new routing engine
82c3ea6 Add '--subnet_prefix' and '--dump_files_dir' options
dfc383e osm_port_info_rcv.c: Optimize PKEY sending during heavy sweep
6abbbcd osm_subnet.c: Add latest Bull device IDs to device white lists
6a24bcd osm_subnet.c: Add additional device ID to is_mlnx_ext_port_info_supported
0ab8d12 Add force_link_width option
3f28045 Handle other 2x and/or HDR not supported SA rate cases
5b56f06 osm_subnet.c: Add additional device IDs to is_mlnx_ext_port_info_supported
f92b33c Add support for additional Mellanox OUIs
b8e2a5b Add option and support for only using the original extended SA rates
deb194f osm_subnet.c: Add additional ConnectX-5 device ID to is_mlnx_ext_port_info_supported
e4e95b3 dfsssp: Replace the internal heap implementation
0dc7b6d Add support for send only full member multicast joins
f8344d0 osm_[base.h helper.c]: Add new Mellanox OUI 0xec0d9a
48457af Add initial policy for long transaction timeout
0b4e9a6 Add timeout parameter for SM class get transactions
2acf61f ib_types.h: Add new rates to return values for ib_[multi]path_rec_rate
f48d4c7 ib_types.h: Add CapabiltyMask2 bit definition for CPI CapabilityMask
012ff1d osm_helper.c: Add support for dumping PortInfo:CapabilityMask2
b937054 ib_types.h: Add additional PortInfo:CapabilityMask2 definitions
ab8b977 [ib_types.h, osm_helper.c]: Change IB_PORT_CAP_RESV13 to IB_PORT_CAP_HAS_CABLE_INFO
771c101 ib_types.h: Add IsPMKeySupported ClassPortInfo CapabilityMask2 bit
ab0671c ib_types.h: Add additional optional counters to PortCountersExtended
abe9803 ib_types.h: Add optional QP1Dropped counter to PortCounters attribute
566222b osm_subnet.c: Add Connect-X5 support to is_mlnx_ext_port_info_supported
f85c48f Add Bull device IDs to device white lists
6531f0b osm_subnet.c: Add support for Bull device ID to is_mlnx_ext_port_info_supported
f30ebc4 osm_[helper.c base.h]: Add support for Bull OUI
fbe5107 osm_subnet.c: Add support for Switch-IB2 in is_mlnx_ext_port_info_supported
6eb8b96 ftree: Additional ftree indexing algorithm
c1f7ffd Support configuration for index 0 in pkey table
1b73efd osm_sa_mcmember_record.c: Conditionalize multicast join parameter
validation on new mcgroup_join_validation option
4cd7ec3 osm_sa_mcmember_record.c: Validate IPoIB non broadcast group
parameters on group creation
fd754d2 Support another new MLNX OUI
66301d8 osm_subnet.c: Add more supported device IDs
948e2b1 Add separate dispatcher for SA set and delete requests
672de65 perfmgr: Add xmit_wait to event plugin error counters data structure
d86e7e3 perfmgr: Add xmit_wait support
7e6bdef opensm/man/opensm.8.in: Add section for MKey support
aebe678 opensm: Add support for multicast service records
5509234 opensm/scripts/sldd.sh: Update to support guid2mkey/neighbors
2ae1477 opensm: Ensure sweep interval/mkey lease are sensibly set
cefe79b opensm: Check for valid mkey protection level in config file
8fa0d2c opensm: Add neighboring link cache file
5088d08 opensm: Log errors on SubnGet timeouts
9eed9c6 opensm: Add support for setting mkey protection levels
5c4157d opensm: Add locking where necessary around osm_req_*
2f74f34 opensm: Allow recovery of subnets with misset mkeys
e5dc557 opensm: Add guid2mkey cache file support
3659b37 opensm/osm_sa_class_port_info.c: Indicate support for PortInfo CapMask2
	matching in SA ClassPortInfo:CapabilityMask2
8016d3b opensm/osm_base.h: Add some SA ClassPortInfo CapabilityMask2 bits
1a31c44 opensm/osm_perfmgr.c: Use non conflicting error codes in log messages
03a75d0 opensm/osm_sa_path_record.c: Restore osm_get_path_params functionality
a9340cf opensm: Support (null) being specified for per_module_logging_file
	option
00375aa opensm/osm_perfmgr.c: Eliminate compile warning
7868c98 opensm: Remove unused per_module_logging option
49c460b Call drop manager before checking for other Master SM in the fabric
c0604f3 Increase p_port->discovery_count only when received PortInfo for port 0
	of the switch
24b30d2 opensm/osm_node_info_rcv.c: Handle non-compliant SMA gracefully
a4f2689 opensm/osm_vendor_ibumad: Add management class into match criteria
4be6375 opensm/osm_sa*.c: Log requester port GUID at DEBUG level
4cca51d opensm/osm_sa_mcmember_record.c: Log requester port GUID at DEBUG level
15b3eae opensm/osm_sa_path_record.c: Log requester port GUID at DEBUG level
0b580ca opensm/osm_sa_path_record.c: Add debug logging to
	pr_match_mgrp_attributes
ed4b7fb opensm/osm_sa_mcmember_record.c: In mcmr_rcv_join_mgrp, add MGID to log
	message
872dae4 opensm/osm_sa_mcmember_record.c: Dump MCMemberRecord in mcmr_query_mgrp
71f2ce7 Sending SL2VL and VLARB SET MADs in distributed manner
f07bcc1 opensm/osm_subnet.c: Cosmetic formatting change
c823a5b opensm/osm_link_mgr.c: Set PortInfo:PortState to LinkDown when remote
	port isn't accessible
ab88df6 Add support to reread configuration file when stacked in rediscovery
	loop.
67c9bae opensm: Move per_mod_log_tbl array from subn to log structure
2cbd9f5 opensm/cl_atomic_osd.h: Cosmetic formatting change
cd63dec opensm/osm_helper.c: Add CapabilityMask2 to notice dump for trap 144
9205812 opensm/ib_types.h: Add CapabilityMask2 to notice for trap 144
25de706 opensm/osm_sa_path_record.c: Add missing end-of-line in the log message
3551530 opensm/osm_trap_rcv.c: Remove vestigial comment
067d217 opensm/osm_inform.c: Make log message format consistent for error
	messages
759b82a opensm/osm_trap_rcv.c: Add better logging for traps 257 and 258
b806657 opensm/osm_sa_mad_ctrl.c: Eliminate commented out code line
a851693 opensm/perfmgr: add logging of error counters
52fa659 opensm/console: add perfmgr "print_errors" (pe) console command.
a832ce2 opensm/console: Add human readable output for perfmgr data counters
77a1756 opensm/console: add abreviations for perfmgr commands
aca9a07 opensm/console; add port option to perfmgr print_counters
00628cc opensm/console: add "print all" to print_counters console command
f5de9b5 opensm: perfmgr mark inactive nodes in perfmgr db
ff06340 opensm: perfmgr delete "inactive" nodes from the DB
059d8f4 opensm/console: protect against 0 entered for the perfmgr sweep_time
cc86607 opensm/perfmgr: Add config option to ignore Channel Adapters.
91f0c00 opensm/osm_node_info_rcv.c: In ni_rcv_process_existing_ca_or_router,
	handle error
c930a23 opensm/osm_subnet.c: Indicate lmc and lmc_esp0 are not changeable
	"on the fly"
7825e67 opensm/libopensm.map: Removed unimplemented routine
7de7b04 opensm/main.c: Handle daemon mode with guid specified as 0 more
	gracefully
9478fbf opensm/osm_subnet.c: Support MLNX ExtendedPortInfo for ConnectIB device
f8fc334 opensm/osm_node_info_rec.c: Also handle non compliant SMA in
	ni_rcv_process_existing
b4a481d opensm: Eliminate unneeded field in DR path structure
01bc8c9 opensm/osm_state_mgr.c: Force subn->need_update when coming out of
	STANDBY
276be8b opensm: Dump info functions update for per module logging
64b512a opensm/osm_vendor_ibumad.c: Make binding log message clearer
e70c8c1 opensm: Add enum for FILE_ID for per module logging
2a2db8c opensm: Add per module logging support
e21b106 opensm: Cosmetic changes
3ddb2e3 opensm: Add partition manager configuration doc to docs
9586649 opensm/opensm_release_notes-3.3.txt: Update Unsupported IB Compliance
	Statements
826b5c4 opensm/osm_ucast_dfsssp.c: Use osm_log_is_active
b2cad9d opensm/complib/cl_fleximap.h: Cosmetic changes
322a310 opensm/osm_ucast_ftree.c: Add a couple of asserts
e3a946d opensm: Add FDR10 support
6cea3df opensm/osm_sa_portinfo_record.c: Add SA PortInfoRecord support
	for CapabilityMask2 matching
9ac7eeb opensm: Add infrastructure support for CapabilityMask2 field in
	PortInfo
8bc7c30 opensm: make loopback console compile on by default.
566b462 opensm/ib_types.h: Update SA PortInfoRecord component masks
480de4a opensm: Use forward extensible and safer way to compare mkey_lmc
	field in PortInfo attribute
22ca966 opensm: enable perfmgr build by default
4250c59 opensm: perfmgr only run sweep timer when enabled
1810672 Support scatter ports
dd21107 Support port shifting
1c2a298 OpenSM torus routing order list
b92d21f opensm: Create all directories in database path on Windows
83b6752 opensm/osm_subnet.c: In osm_subn_destroy, delete service records
0fc8124 opensm: Add OSM_VENDOR_ID_OPENIB support
c0d8b56 opensm/osm_sa_node_record.c: In nr_rcv_create_nr, only set some
	variables when needed
9acaba0 opensm/osm_helper.c: Add some missing fields to
	osm_dump_portinfo_record
8a43aea Allow comma in plugin names parsing
cdf227c opensm: Proper mfttop initialization when starting/restarting
cde0c0d opensm: Convert remaining helper routines for GID printing format
bc5743c opensm: Add support for MaxCreditHint and LinkRoundTripLatency to
	osm_dump_port_info
6cd34ab opensm: Add Dell to known vendor list
003d6bd opensm: Add more info for traps 144 and 256-259 in osm_dump_notice
5b0c5de opensm/osm_ucat_ftree.c Enhance min hops counters usage
0715b92 ib_types.h: Add ib_switch_info_get_state_opt_sl2vlmapping routine
2ddba79 opensm: Remove some __ and __osm_ prefixes
ea0691f opensm/iba/ib_types.h: Add PortXmit/RcvDataSL PerfMgt attributes
9c79be5 ib_types.h: Adding BKEY violation trap (259)
c608ea6 opensm: Add and utilize ib_gid_is_notzero routine
b639e64 opensm: Handle trap repress on trap 144 generation
b034205 Add pkey table support to osm_get_all_port_attr
876605b opensm/ib_types.h: Add attribute ID for PortCountersExtended
aae3bbc opensm: PortInfo requests for discovered switches
0147b09 opensm/osm_lid_mgr: use single array for used_lids
a9225b0 opensm/Makefile.am: remove osm_build_id.h junk file generation
8e3a57d opensm/osm_console.c: Add list of SMs to status command
3d664b9 opensm/osm_console.c : Added dump_portguid function to console to
	generate a list of port guids matching one or more regexps
85b35bc opensm/osm_helper.c: print port number as decimal
8674cb7 opensm: sort port order for routing by switch loads
80c0d48 opensm: rescan config file even in standby
8b7aa5e opensm/osm_subnet.c enable log_max_size opt update
8558ee5 opensm/include/iba/ib_types.h: Add xmit_wait for PortCounters
ecde2f7 opensm/osm_subnet.c support subnet configuration rescan and update
58c45e4 opensm/osm_log.c save log_max_size in subnet opt in MB
cf88e93 opensm: Add new partition keyword for all hca, switches and routers
4bfd4e0 opensm: remove libibcommon build dependencies
3718fc4 opensm/event_plugin: link opensm with -rdynamic flag
587ce14 opensm/osm_inform.c report IB traps to plugin
ced5a6e opensm/opensm/osm_console.c: move reporting of plugins to "status"
	command.
696aca2 opensm: Add configurable retries for transactions
0d932ff opensm/osm_sa_mcmember_record.c: optimization in zero mgid comparison
254c2ef opensm/osm_sm_mad_ctrl.c: In sm_mad_ctrl_send_err_cb, set init
	failure on PKeyTable and QoS initialization failure
83bd10a opensm: Reduce heap consumption by multicast routing tables (MFTs)
cd33bc5 opensm: Add some additional HP vendor IDs/OUIs
f78ec3a opensm/osm_mcast_tbl.(h c): Make max_mlid_ho be maximum MLID configured
2d13530 opensm: Add infrastructure support for PortInfo
	IsMulticastPkeyTrapSuppressionSupported
3ace760 opensm: Reduce heap consumption by unicast routing tables (LFTs)
eec568e osmtest: Add SA get PathRecord stress test
aabc476 opensm: Add infrastructure support for more newly allocated PortInfo
	CapabilityMask bits
c83c331 opensm: improve multicast re-routing requests processing
46db92f opensm: Parallelize (Stripe) MFT sets across switches
00c6a6e opensm: Parallelize (Stripe) LFT sets across switches
e21c651 opensm/osm_base.h: Add new SA ClassPortInfo:CapabilityMask2 bit
	allocations
09056b1 opensm/ib_types.h: Add CounterSelect2 field to PortCounters attribute
6a63003 opensm: Add ability to configure SMSL
25f071f opensm/lash: Set minimum VL for LASH to use
622d853 opensm/osm_ucast_ftree.cd: Added support for same level links
8146ba7 opensm: Add new Sun vendor ID
1d7dd18 opensm/osm_ucast_ftree.c: Enhanced Fat-Tree algorithm
e07a2f1 Add LMC support to DOR routing
1acfe8a opensm: Add SuperMicro to list of recognized vendors
f02f40e opensm: implement 'connect_roots' option in fat-tree routing
748d41e opensm SA DB dump/restore: added option to dump SA DB on every sweep
b03a95e complib/cl_fleximap: add cl_fmap_match() function
b7a8a87 opensm/include/iba/ib_types.h: adding Congestion Control definitions
fa356f8 opensm: Add support for optimized SLtoVLMappingTable programming
8aaae91 Dimension port order file support
7662eec opensm: Add option to specify prefix to syslog messages
2382cf3 opensm: Add update_desc command to opensm console
7cbe193 opensm: toggle sweeping V3
6f61d8f opensm/osmeventplugin: added new events to monitor SM
84cf603 opensm/main.c: force stdout to be line-buffered
b3bb0ab opensm/osm_dump.c: Dump SL2VL tables if routing engine might have
	modified them
8a08719 opensm/osm_dump.c: dump SL2VL tables in debug verbosity level when
	QoS is on
fc908c9 opensm/osm_sa_multipath_record.c: Add mtu validation if supplied
687e1f8 opensm/osm_sa_mcmember_record.c: Add mtu validation if supplied
76f5b09 opensm/osm_sa_path_record.c: Add mtu validation if supplied
9f38fae opensm/osm_helper: Add ib_mtu_is_valid
655230b opensm/osm_sa_multipath_record.c: Add rate validation if supplied
cb1484d opensm/osm_sa_mcmember_record.c: Add rate validation if supplied
485d068 opensm/osm_sa_path_record.c: Add rate validation if supplied
300f4d9 opensm/osm_helper: Add ib_rate_is_valid
9b50961 opensm: Change osm_routing_engine struct to not use C++ reserved word
c9c0aa6 opensm/man/torus-2QoS.conf.5.in: Update portgroup_max_ports section
3c97f06 opensm: Add the precreation of multicast groups
a9b9f09 opensm/osm_sminfo_rcv.c: Handle SMP status
1190c15 opensm/osm_switch.c: In osm_switch_set_hops, return, error when port_num is invalid
3d149db Changed sl_path API to include slid and dlid only
6cfb0eb Optimized and deadlock-free routing algorithm for InfiniBand
45f93ec opensm: Add additional IBM vendor ID/OUI
c386eb2 opensm/osm_state_mgr.c: Cosmetic change to log message
703e596 opensm: Add support for partition enforcement types to accomodate IBA extended link speeds
a2a03a8 Check block_num validity in set_guidinfo() and del_guidinfo() requests
e6ec61f end error resoponse to invalid LID in GUIDInfo request
7fce500 opensm/Makefile.am: Add doc/opensm-sriov.txt to docs
264aeb1 opensm: Add documentation for SRIOV support
c639832 opensm: Enhance osm_physp_share_this_pkey for allow_both_pkeys policy
b17b63c opensm: When allowing both pkeys, on a switch external, (peer) port eliminate limited pkey when full pkey with same base is present
a758da2 opensm: Add command line option for allow_both_pkeys
f412de3 opensm: Update partition documentation and man page for (allowing) both (limited and full) memberships in the same partition
726ce6a Support allowing both full and limited members of same partition
4ccf32f opensm/PKeyMgr: Support pkey index reuse when there are no longer any previously unused indices available
eb375a6 opensm/osm_pkey_mgr.c: Detect pkey table overflow in pkey_mgr_update_port
411e742 opensm/PkeyMgr: Don't change end port pkey index when simultaneously adding and removing partitions
15e7223 opensm/osm_sa_guidinfo_record.c: In set_guidinfo, better SM reassigned guid handing
e79b725 opensm: Handle SubnSet GUIDInfo asynchronously from GUIDInfoRecord handling
96c741d opensm: Some cosmetic formatting changes
1d5e370 opensm/osm_sa_guidinfo_record.c: Better status for SA response
efd3ba2 opensm/osm_sa.c: Change log level of message
db8b7da opensm/osm_sa_service_record.c: Alias GUID support
5330986 opensm/osm_sa_multipath_record.c: Add support for alias GUIDs
44168c9 opensm/osm_sa_guidinfo_record.c: In del_guidinfo, validate guid not in use
63eb65b opensm: Add multicast support for alias GUIDs
700d15f opensm/osm_sa_path_record.c: Add support for alias GUIDs
f818387 opensm/osm_sa_guidinfo_record.c: Use OSM_VENDOR_ID_OPENIB define rather than IB_OPENIB_OUI
97e360e opensm: Dump/load SA GUIDInfoRecords
fe74f1d opensm: Make SA assigned guids persistent across port down/up events
eb8f1d9 opensm: Add support for alias GUIDs
b3b1861 opensm: osm_subnet.c: Updated patch to add error-reporting to the parsing of opensm.conf
cd8a708 opensm/man/opensm.8.in: Add description for OSM_LOG_SYS logging flag
1308e5c opensm/osm_console.c: Add display of FDR10 ports to portstatus_parse
f4722b0 opensm: Reset client reregistration when receiving handover
5fdb0b9 opensm/configure.in: Remove Default-Start from opensmd init script
b966100 opensm/osm_sm_state_mgr.c: Start sweep immedeately when recieving HANDOVER in DISCOVERING state
4349c07 opensm: Add physp_p discovery count support
d706cbf opensm/osm_sm_mad_ctrl.c: Upon receiving trap repress we should decrease qp0_mads_outstanding_on_wire
e1e4706 opensm: Revert "opensm/osm_ucast_ftree: When roots are not connected, update hop count but not lft"
7a49366 opensm: Changed #if to #ifdef when using ENABLE_OSM_PERF_MGR_PROFILE
34af1a5 opensm/osm_torus.c: torus routing should fail with VLCap 1 on switch external ports
2b8940b opensm/osm_torus.c: In dump_torus, make sure switch is present before dumping
2364e8e opensm: Add better error output when parsing node name maps
7204287 opensm/osm_sa_mcmember_record.c: Only use scope from MC group in copy_from_create_mc_rec
4eb0f69 opensm/osm_dump.c: Remove incorrect assert
1ddb44b OpenSM: dfsssp - moved paths from one to another VL might be counted multiple times
a0a9eea OpenSM: DFSSSP does not find LIDs due to wrong byte order (v2)
13fd952 OpenSM: dfsssp - avoid unnecessary nested loop in vltable_print for OSM_LOG_INFO
1cbed64 OpenSM: dfsssp - change the port traversal for sssp
c2f0bb9 OpenSM: dfsssp - add support for base/enhanced switch port
64873ff OpenSM: dfsssp ignores differences in the lmc value
162493e opensm/osm_req.c: In req_determine_mkey, use osm_physp APIs
fc53bdc opensm/osm_torus.c: Dump torus when OSM_LOG_ROUTING specified
434afa6 opensm/torus: Add configuration for max_changes to report
35bcddf opensm/osm_torus.c: Consolidate some parsing with parse_unsigned
3f3d26f opensm/osm_prtn_config.c: Insert autogenerated pkey into MGID for IPoIB
d80ca1e opensm/osm_port_info_rcv.c: Don't modify subnet minimal values when PortState DOWN
f97a2e9 opensm/osm_ucast_ftree.c: Eliminate unneeded NULL pointer checks prior to calls to free
8a77f23 opensm/osm_ucast_ftree.c: Remove duplicate free in fabric_create_leaf_switch_array
338a67b opensm/osm_subnet.c: Improve error messages in subn_validate_neighbor
0a1c9bf opensm/osm_req.c: In req_determine_mkey, add more info when ERR 1107 occurs
b974ca3 opensm/osm_torus.c: Improve some misconfiguration error messages
e986d57 opensm/osm_torus.c: Minor simplification to check_qos_config
95b6f83 opensm: Update doc for changes to torus routing for, endport support
998fb43 opensm/osm_torus.c: Add copyright
268d327 opensm/osm_torus.c: Improve QoS configuration
d1d2de5 opensm: Update doc for changes to torus routing for CA, support
a362b5b opensm/osm_torus.c: Require only 2 data VLs supported (PortInfo.VLCap) and use VLs 0-1 on CA links
3f536ae opensm: Add routing specific update_vlarb hook routine
a2415f3 Add Per Module Logging support for Congestion Manager
a6d3694 opensm: clean up error message, function name is printed by logging code
2cf8078 opensm/osm_sa_mcmember_record.c: Return proper scope for query with valid SA key
c1a206e opensm: update performance manager documentation
f204e9c opensm: Protect against spurious wakeups when calling cl_event_wait_on
7db5c75 opensm/osm_subnet.c: Only parameters that marked with can_update flag should be updated during conf file rescan
282714a /etc/init.d/opensmd: Improve systemd integration
8b116d7 opensm/osm_trap_rcv.c: Eliminate unneeded trap_rcv_process_response routine
4008c2f opensm.spec.in: Improve portability
01ab744 Make it possible to enable opensm with chkconfig
c68ae63 Add command-line option --pidfile
ffb9a82 opensm/osm_torus.c: Check fabric minimum data VLs on switch external ports
d0258f3 opensm: Track minimum value in the fabric for data VLs supported on switch external ports
36159c2 opensm/osm_torus.c: Cosmetic formatting change
dba3f92 opensm/osm_helper.c: Add some missing new lines to log message output
94468b5 opensm/osm_ucast_updn.c: Add error codes to a couple of log messages
86a203b Correct option names in opensm man page
3482438 opensm/osm_sa.h: Cosmetic commentary change
824e1f0 opensm/ib_types.h: Commentary and cosmetic formatting change
3711e8a opensm/complib/cl_spinlock.h: Remove some unimplemented routines
0c3f57f opensm/osm_torus.c: Add error code to error log message
8e6dfbd opensm: Add .gitignore
6248495 opensm/osm_port_info_rcv.c: use PF() hint on fatal conditions
dff5927 opensm/osm_port_info_rcv.c: check received local_port_num
b7b1505 opensm/libvendor/osm_vendor_ibumad.c: validate response MAD properties
c215eea opensm/libvendor/osm_vendor_ibumad.c:rename "mad" to "p_mad" to indicate pointer
e00f67b opensm/libvendor/osm_vendor_ibumad_sa.c: use wrapper function instead of direct access
ebc772c opensm/osm_node_info_rcv.c: using "PF" hint for all the fatal conditions
e2f06cd opensm/complib: define macros for for "if" statements with branch prediction hints
0e33c11 opensm/configure.in: check that compiler supports __builtin_expect()
2cb1854 OpenSM: Add new Mellanox OUI
8e5fc57 opensm: osm_pkey: Remove unused variables
e96a933 opensm: Manage ports that do not support congestion control
fbb74ca opensm: improve search common pkeys.
db34e30 opensm/osm_vendor_ibumad.c: Add management class to error log message
ba36f81 opensm/perfmgr/console: add 'pm sweep'
f118668 opensm/osm_sa: Improve SA record response handling (osm_sa_respond)
b09ac65 opensm/osm_sa_informinfo.c: Add trusted support for InformInfo/InformInfoRecord
5b2390e dfsssp: optimization for dedicated compute and IO nodes
33f4f17 Add new option for guid_routing_order_no_scatter
a5f54f1 osm_ucast_mgr.c: After applying guid_order options, add nodes sorted
by neighbor switch load
5a97c78 Try default partition config if parsing partitions.conf fails
cc5fa17 Add support for synchronizing in memory files with storage
9853841 Implement atomic update operation for sa_db_file
3e146fa Add option to disable M_Key lookup
51b187b Add flags to OSM_EVENT_ID_UCAST_ROUTING_DONE
c97bd7b Permit toggling log flush from console
8395084 OpenSM: dfsssp - add support for multicast
96eafa0 ftree: Allow defining only io_guids file and consider rest of nodes as
CN nodes
6cd6114 opensm: Add support for LFT changed event
b73c378 opensm/perfmgr; add support for PortCountersExtended NOIETF
70f5820 opensm/perfmgr: Issue PortCountersExtended query when supported

1.3 Library API Changes

2fd785f Deprecate complib_init() due to use of exit() function
399fe25 Eliminate circular dependencies in shared libraries
d7135b1 complib/cl_types.h: Remove unimplemented function cl_panic
04b0c34 complib: cl_vector_copy16/32/64 should be static functions
925e1ce Add timeout parameter for SM class set transactions
f60e241 osm_madw.h: Remove unused bind_info in osm_madw structure
aa63c3c complib: Add a d-ary heap
44a6358 libvendor/osm_vendor_ibumad.c: Support GRH (for GS classes)
de02954 osm_trap_rcv.c: Log DR path to node when trap 128

1.4 Software Dependencies

OpenSM depends on the installation of libibumad package (distributed as
part of OFA IB management together with OpenSM) and IB stack presence,
in particular libibumad uses user_mad kernel interface ('ib_umad' kernel
module). The qualified driver versions are provided in Table 2,
"Qualified IB Stacks".

Also, building of QoS manager policy file parser requires flex, and either
bison or byacc installed.

1.5 Supported Devices Firmware

The main task of OpenSM is to initialize InfiniBand devices. The
qualified devices and their corresponding firmware versions
are listed in Table 3.

2 Known Issues And Limitations
------------------------------

* No Service / Key associations:
  There is no way to manage Service access by Keys.

* No SM to SM SMDB synchronization:
  Puts the burden of re-registering services, multicast groups, and
  inform-info on the client application (or IB access layer core).

3 Unsupported IB Compliance Statements
--------------------------------------
The following section lists all the IB compliance statements which
OpenSM does not support. Please refer to the IB specification for detailed
information regarding each compliance statement.

* C14-22 (Authentication):
  M_Key M_KeyProtectBits and M_KeyLeasePeriod shall be set in one
  SubnSet method. As a work-around, an OpenSM option is provided for
  defining the protect bits.

* C14-67 (Authentication):
  On SubnGet(SMInfo) and SubnSet(SMInfo) - if M_Key is not zero then
  the SM shall generate a SubnGetResp if the M_Key matches, or
  silently drop the packet if M_Key does not match.

* C15-******** (Authentication):
  InformInfoRecords shall always be provided with the QPN set to 0,
  except for the case of a trusted request, in which case the actual
  subscriber QPN shall be returned.

* o13-17.1.2 (Event-FWD):
  If no permission to forward, the subscription should be removed and
  no further forwarding should occur.

* C14-44 (Initialization):
  If the SM discovers that it is missing an M_Key to update CA/RT/SW,
  it should notify the higher level.

* C14-********* (Initialization):
  PortInfo:M_Key - Set the M_Key to a node based random value.

* C14-********* (Initialization):
  PortInfo:M_KeyProtectBits - set according to an optional policy.

* C14-********* (Initialization):
  SwitchInfo:DefaultPort - should be configured for random FDB.

* C14-********* (Initialization):
  RandomForwardingTable should be configured.

* o15-0.1.12 (Multicast):
  If the JoinState is SendOnlyNonMember = 1 (only), then the endport
  should join as sender only.

* o15-0.1.8 (Multicast):
  If a request for creating an MCG with fields that cannot be met,
  return ERR_REQ_INVALID (currently ignores SL and FlowLabelTClass).

* C15-******* (SA-Query):
  Respond to SubnAdmGetTraceTable - this is an optional attribute.

* C15-0.1.13 Services:
  Reject ServiceRecord create, modify or delete if the given
  ServiceP_Key does not match the one included in the ServiceGID port
  and the port that sent the request.

* C15-0.1.14 (Services):
  Provide means to associate service name and ServiceKeys.

4 Bug Fixes
-----------

4.1 Major Bug Fixes

73f6461 osm_opensm.c: Fix seg fault in destroy_routing_engine
9a7fa48 osm_multicast.c: Fix potential crash in osm_mgrp_delete_port
af9fc7a osm_sa_mcmember_record.c: Use neighbor MTU rather than MTUCap in mgrp_request_is_realizable
9a4e79b dfsssp - detect and try to repair an odd network state
85f841c osm_ucast_[dfsssp ftree].c: Fix memory leak when io/cn guid file have duplicated guids
8980f12 osm_port_info_rcv.c: Fix min_ca_rate determination in pi_rcv_process_endport
a7db80a osm_lid_mgr.c: Clean up LID ranges that are beyond current LMC setting
677bc59 osm_ucast_updn.c: Add memory allocation failure handling in updn_build_lid_matrices
2f4713d osm_qos_policy.c: Partition SL independence from QoS matching rules
0d88e64 osm_state_mgr.c: opensm does not generate SA_DB_DUMPED event after heavy sweep
a686cec osm_ucast_mgr.c: Fix minhop tables miscalculation due to variable wraparound
aa7d21a Fix SM-Key management
f6af5af osm_port_info_rcv.c: Fix bug in portinfo set failure handling
62ea61a osm_state_mgr.c: Fix bug in handling of PortInfo Set timeout
34e81df Change precedence in pkey manager with indx0 feature
aa905e2 osm_pkey.[h c], osm_prtn.c: Create method to set pkey at indx0
8a696eb osm_base.h: Fix OSM_CAP2_IS_PORT_INFO_CAPMASK2_MATCH_SUPPORTED
definition
70a364f opensm/osm_pkey_mgr.c: Fix pkey index wraparound/reuse
6e27bba osm_multicast.c: Dump sa after deleting well known mc group
df212a6 osm_multicast.c: Fix not dumping empty predefined mc groups
1007810 osm_sa_guidinfo_record.c: Fix memory leak in set_guidinfo
6272a56 osm_ucast_dfsssp.c: In dfsssp_do_mcast_routing, update adj_list first
44b4edc osm_ucast_mgr.c: LFT update breaks if IB_SMP_DATA_SIZE changes
19dbb67 osm_ucast_dfsssp.c: Fix dangling pointer when dfsssp used with ucast
cache
76bd8cd osm_sa_mcmember_record.c: Add error logging for mismatches to
validate_other_comp_fields
122cad3 osm_sa_mcmember_record.c: When joining MC group and MC group already
exists, validate other components if supplied
909976d Add more checks for physp validity
b37560b osm_mcast_mgr.c: Add check for physp validity to create_mgrp_switch_map
e4e449e osm_ucast_ftree.c: Fix wrong is_io assignment to the second HCA port
e9bbf44 osm_ucast_ftree.c: Fix wrong handling of dual port HCA
ccab3b8 osm_ucast_ftree.c: ftree calculates wrong number of compute nodes per
leaf
06ae3ad osm_sa.c: Fix osm_db_file_dump
f1ab545 osm_drop_mgr.c: Do not set SM port discovery count to 0 when switch
remote port is nonresponsive
cd12dcd osm_port_info_rcv.c: Fix calculation of minimum data VLs
cb20b11 osm_ucast_ftree.c: Fail routing if max rank is 0 and more than one
root
c633e05 osm_ucast_ftree.c: Mark HCA ports connected to unranked switches
f004819 osm_sa.c: Fix race condition when writing SA DB file
a64de2e osm_prtn_config.c: Fix wrong pkey table calculation in
allow_both_pkeys mode
0b6d8ea osm_sa_mcmember_record.c: Validate port's neighbor_mtu when joining MC
group
80ca401 osm_sa_path_record.c: When both [D S] GID and LID are supplied,
validate that underlying ports are the same
1fa8089 osm_sa_path_record.c: Handle path query by DGID and SLID
141293d osmtest/osmt_multicast.c: Fix MC join with unrealistic rate test
f471571 osm_subnet.c: Remove support for changing allow_both_pkeys on the fly
cd31ff6 osm_port_info_rcv.c: Sending MEPI(Get) to local switch ports on hop 0
4c3d782 osm_state_mgr.c: Mark port as undiscovered when removed by drop_mgr
a1c4bc2 osm_console.c: Fix display of negative counters in console
a6c2bf8 Add cleanup of SA cache after handover
075ff44 osm_mcast_tbl.c: Wrong assert placement in osm_mcast_tbl_get_block
953b70d osm_opensm.c: Fix race condition between traps handling and SA
shutdown
5e50a3c osm_sa_mcmember_record.c: Extend P_Pkey validation on MCMemberRecord(Join)
3fcb121 osm_subnet.c: Improved m_key_lookup description in generated conf file
b4d20d7 osm_port_info_rcv.c: In pi_rcv_process_switch_port0, store port 0's
PortInfo before querying switch external ports
062d35d osm_port_info_rcv.c: In pi_rcv_process_switch_port0, determine mkey
899664d osm_ucast_mgr.c: Support diverse paths for LMC > 0 when scatter_ports
is enabled
6dc0b80 osm_opensm.c: Call cl_disp_shutdown for SA SET dispatcher
3199d34 osmeventplugin.c: Modify osm_event_plugin_t initialization
94c2d74 Revert "osm_ucast_mgr.c: Force unicast routing to fail when lft
allocations fail" patch
1d205bc osm_ucast_mgr.c: Select exit ports for lid offset > 0 using new_lft
2a32095 osm_ucast_cache.c: Rewrite p_sw->lft allocation in osm_ucast_cache_process
e2ee83e Use routing engine calculated LFT for SA queries
1413d64 osm_state_mgr.c: Remove new_lft buffers cleanup
f2b96de osm_ucast_mgr.c: Force unicast routing to fail when lft allocations fail
8ea4e06 osm_congestion_control.c: Reset cc_timeout_count when count threshold passed
0b7c80c osm_congestion_control.c: Consistently set cc_unavailable_flag
e590ebc osm_congestion_control.c: Add additional header status check in cc_rcv_mad
f130d5a osm_subnet.c: Correct output error with congestion control table entries
0a695ae osm_switch.c: Fix wrong assertion failed in osm_switch_get_lft_block()
5077198 osm_perfmgr.c: Reset physp_discovered before discovery
8d51ae5 osm_state_mgr.c: Avoid continuing discovery when SM port is
unresponsive
f017063 Better way to handle polling other MASTER SM
25e5ee5 complib/cl_dispatcher.c: Check registrations vector size when
searching for handlers
6b0fb4d osm_lid_mgr.c: Send client reregistration in case of SM LID change
5d231f8 osm_pkey_mgr.c: Use calloc instead of malloc in
pkey_mgr_process_physical_port
985859e Fix turning on first_time_master_sweep flag
4629e80 osm_mcast_mgr.c: Invalidate cache due to multicast routing errors
b6a1dd4 osm_state_mgr.c: Avoid ucast cache invalidation due to errors during
initialization
db69cd8 osm_perfmgr.c: Fix perfmgr sweep_state race
b42f11f osm_vendor_ibumad.c: Better match table eviction strategy
cc0d61b libvendor/osm_vendor_ibumad.c: Check the next CA if an error is
returned for the current one
a16349d osm_trap_rcv.c: Lock released without locking in some cases of trap
processing
6c6e4cc osm_sa_guidinfo_record.c: Fix crash when receiving AGUID SET/DELETE
with block_num equal to max_block
a8b23b7 osm_vl15intf.c: Fix potential NULL dereference in vl15_send_mad
e94d471 osm_torus.c: Fix torus crash when actual topology is not torus
a20cd5f osm_sa_service_record.c: Fix locking issue in osm_sr_rcv_process
127acf1 osm_sa_mcmember_record.c: Fix double locking in mcmr_rcv_join_mgrp
c86c30a All SA queries should validate the requester port under lock
80e1e59 osm_sminfo_rcv.c: Send trap 144 to a newly found MASTER SM when in
MASTER state
0189dea osm_sm_state_mgr.c: Fix opensm crash after handover
52c4a30 osm_sm_state_mgr.c: Reconfigure the fabric when receiving
HANDOVER/POLLING_TIMEOUT in MASTER state
26af3a9 Fix crash during handover
f775a1a osm_sa_path_record.c: Search for requester port should be under lock
24d6219 osm_sw_info_rcv.c: Fix sending PortInfo during lightsweep
12d2c8e osm_node_info_rcv.c: Fix multiple switch discovery during a sweep
ef22eb9 osm_qos.c: Fix possible seg fault
9dd8f45 dfsssp: avoid crash due to port initialisation errors
f6418cf osm_ucast_dfsssp.c: prevent double free error
1633550 Resend LFTs/VLArb/SL2VL MADs in case of error
90d19c2 osm_port_info_rcv.c: Reread pkeys from SP0 if switch rebooted during a
sweep
6e90421 Better handling of topology changes in the fabric
b33c3ba Change discovery order of switch data
395b5f0 Handle bad SMP status
a322f51 Skip TID 0 on 32 bit wraparound for SMP, SA and PerfMgt queries
cded9af Fix transaction id casting
3585f8b opensm: Fix crash found with ucast cache
74e12d9 opensm: fix part_enforce parameter parsing crash
647a98e Fixed crash in sm_state_mgr_send_master_sm_info_req() during fabric
	merge
63ebd0d Fix crash in ucast cache when chain of switches connected back at once
	to the fabric
3b21d6f Fix crash in ucast cache when ucast cache invalidates after updating
	one of the switches
5654e22 Fix invalid error check, which lead to segfault
7bf7482 fix segfault corner case w/ updn routing and LMC > 0
18990fa opensm: set IS_SM bit during opensm init
3551389 fix local port smlid in osm_send_trap144()
a6de48d opensm/osm_link_mgr.c initialize SMSL
82df467 opensm/osm_req.c: Shouldn't reveal port's MKey on Trap method
45ebff9 opensm/osm_console_io.h: Modify osm_console_exit so only the
	connection is killed, not the socket
d10660a opensm/osm_req.c: In osm_send_trap144, set producer type according
	to node type
8a2d2dd opensm/osm_node_info_rcv.c: create physp for the newly discovered
	port of the known node
39b241f opensm/lid_mgr: fix duplicated lid assignment
b44c398 opensm: invalidate routing cache when entering master state
595f2e3 opensm: update LFTs when entering master
8406c65 opensm: fix port chooser
fa90512 opensm/osm_vendor_*_sa: fix incompatibility with QLogic SM
7ec9f7c opensm: discard multicast SA PR with wildcard DGID
5cdb53f opensm/osm_sa_node_record.c use comp mask to match by LID or GUID
55f9772 opensm: Return single PathRecord for SubnAdmGet with DGID/SGID wild
	carded
5ec0b5f opensm: compress IPV6 SNM groups to use a single MLID
26e7e83 opensm/osm_lid_mgr: fix couple of duplicate LIDs bugs
fedc419 opensm: Multicast root switch calculation
6772fdb opensm: Fix sl2vl configuration
dd3470f complib/cl_timer.c: fixing cl_timer calculation
ae1bcdd opensm/osm_lid_mgr.c: Allow switch lids to be non LMC aligned
593547e Wrong handling of MC create and delete traps
4c822b0 opensm/osm_prtn.c: removing TopSpin hack
8214e2a opensm: Add support for SwitchInfo:MulticastFDBTop
1e544ba opensm: fixed memory leak in multicast spanning tree calculation
0456b3f Fixed multicast groups reconfiguration during heawy sweep
9ad844f Fix ucast cache crash, when switch doesn't have valid phys ports
07aa9fa opensm: fix crash in osm_ucast_mgr
076bd38 opensm/osm_ucast_cache.c: fix crash in ucast cache when switch with lid 0 dropped
9cbb8af opensm/osm_ucast_ftree.c: fix opensm segfault in osm_ucast_ftree.c
edd0552 opensm/osm_qos_policy.c: fix segmentation fault on
	osm_qos_policy_match_rule_destroy (osm_qos_policy.c)
dec1109 DFSSSP: fix a memory leak in dfsssp_build_graph
d5fe528 opensm/osm_req.c: In req_determine_mkey, fix DR algorithm
a46b33e opensm: fix crash in DFSSSP routing engine on reroute
d2312af opensm/osm_link_mgr.c: Fix sending PortInfo(Set) with AM SMSupportExtendedSpeeds
	bit set for switch base port 0
06ae82c opensm/osm_link_mgr.c: Set AM SMSupportExtendedSpeeds bit if port
	supports ExtPortInfo
b2cd2d7 opensm/osm_ucast_ftree.c: Fix unranked nodes bug in FTree
8384156 opensm/osm_torus.c: Fix crash in torus_update_osm_vlarb
0ff054a osm_trap_rcv.c: Removed unneeded lock when disabling port
e7c4ec3 osm_trap_rcv.c: Minimize time holding RW lock for SystemImageGUID
changed trap 145
1418f8a osm_trap_rcv.c: In trap_rcv_process_request, change locking strategy
280a2ac osm_trap_rcv.c: fix locking in trap_rcv_process_request()
a45e311 osm_db_files.c: Minor improvement to fix in previous commit
86cf679666f49f6073c1ddf2b9ff644a41537a57
86cf679 osm_db_files.c: Fix issue introduced in commit
aaa7b1e67ec6e5fc2a10accf46d538f9d47c6323
ed7be1a Add client_rereg flag to Port Info context
413e4fa osm_trap_rcv.c: fix race condition during sweep
90a7960 osm_ucast_file.c: Fix crash when port is invalid in LFT file
a1ccf88 osm_sa_path_record.c: path_sl may return SL different from requested
SL
8f3f7d0 osm_sm_state_mgr.c: Fix race condition during
sm_state_mgr_send_master_sm_info_req
b1b1b37 Fix minhop population in fabric with duplicate lids
ed9de4c osm_mcast_mgr.c: Fix wrong comparison in mcast_mgr_subdivide()
c5d5faa Only rewrite db files during heavy sweep when there is a real change
9aa6dcd osm_drop_mgr.c: fix timeouts on Get Pkey from ext switch ports
361c014 Fix dropping node after setPkey mad
2585f58 Improve memory consumption of pkey manager by using cl_map for
accum_pkeys
754bd75 osm_opensm.c: When exiting, update SADB only in MASTER state
3cc0a3e Fix timeout handling for pkeyGet for sw port 0
189a39d Fix possible use of lid 0 when sending set PortInfo after failure of
the first PortInfo set
6d1d1a4 Fix handling of get P_KeyTable on timeout
df385e8 osm_ucast_cache.c: Fix memory leak in ucast_cache
79ba877 osm_link_mgr.c: active_transition parameter in PortInfo(Set) context
may not be initialized
b442062 Resend trap 144 when detecting remote MASTER SM with lower priority
49ea151 osm_pkey.c: Fix find common pkey bug fix
e3aa9e8 dfsssp: send multicast forwarding tables to switches
4a39fb1 dfsssp: send multicast forwarding tables to switches
d5f6e25 osm_sa_mcmember_record.c: Fix incorrect comparison of IPv6 MGID when
searching for SNM MLID
908c524 osm_guid_mgr.c: Fix GUIDInfo SET function
e8bf592 Clean up event subscriptions if a port goes away
7c9afa0 Improve m_key lookup
807d9ed osm_trap_rcv.c: Fix locking in aging callback
eb294d5 osm_lid_mgr.c: Don't configure MTU and LMC for base SP0
a47e5d0 complib/cl_event_wheel.c: Roundup timeout to nearest msec
2a8c474 complib: Fix memory leak in cl_thread_pool_destroy function
1960fbc osm_trap_rcv.c: Fix crash in babbling port feature
1872d11 osm_sa_sminfo_record.c: fix sminfo sa query returns all sminfo records
when filtering by LID in osm_smir_rcv_process
b92f25a osm_ucast_cache.c : Fix dereference null return value
21765ad When SM fails to load/parse root_guids file use MinHop heuristics
7c21c14 Fix shift pattern support in FTREE routing for native ftree topologies
35f4419 osm_ucast_ftree.c: fix dereferencing null variable
4d36d6d osm_ucast_ftree.c : fix dereferencing null variable
49f1a71 opensm: fix possible double free in osm_ucast_ftree.c
2695128 Fix fat-tree routing for CAs with more than 1 connected port
42558d6 osm_sm_state_mgr.c Don't clear IS_SM bit when changing state to
NOT_ACTIVE
21a5b5f DFSSSP - workaround for better VL balancing
b42ae68 osm_console_io.c Memory leak when closing console
bcf00cb osm_ucast_dfsssp.c: Fix memory leak in dfsssp_do_dijkstra_routing
f42a232 Fix segfault in osm_mgrp_delete_port()
1a94326 osm_sa_multipath_record.c Use aliasGUIDs when building responses
943a40a opensm: Fix Q_Key, TClass and limited keys parsing warnings in
partitions.conf
0fc6e8b osm_lid_mgr.c: Fix duplicate LID assignment after SM port down
becc27e osm_sa_mad_ctrl.c: Drop incoming SA queries when shutting down
dbeb7a7 Change LFT event to be per block/per switch rather than just per
switch
25c088a Setup SM port GUID in subnet object as soon as it is known
92c502a osm_sa_mcmember_record.c: On join and leave, validate subnet prefix in
port GID
c0602ed opensm/osm_port_info_rcv.c: Fix min_sw_data_vls calculation
2d8df36 opensm/osm_sw_info_rcv.c: Fix double release of lock in
osm_si_rcv_process
e7435ef opensm/osm_torus.c: avoid the possibility of following stale ->priv
pointers
904a555 opensm/perfmgr: fix access to shared sweep_state variable
1458263 opensm/perfmgr: don't clear data counters in PortCounters when
ExtendedPortCounters is supported
b6d0001 opensm/perfmgr: issue ClassPortInfo as first query to each port.
e1af1ce opensm/osm_console.c: Use ib_port_info_get_link_speed_ext_enabled
2ba9919 opensm/osm_console.c: Do not perform portstatus checks on down ports
5c45f60 opensm/osm_console.c: Support portstatus output for unenabled
width/speed
1947644 opensm/osm_qos_policy.c: fix memory leak when parsing policy file
9358164 opensm/osm_sa_multipath_record.c: Validate required components are
indicated
fbe0d02 opensm/osm_sa_multipath_record.c: Make sure either none or both
ServiceID parameters are supplied
f929ac1 opensm/osm_sa_path_record.c: Make sure either none or both ServiceID
parameters are supplied
43efbc2 opensm/osm_sa_multipath_record.c: Fix seg fault in
mpr_rcv_get_apm_paths
250802d opensm/osm_qos_policy.c: Fix source & destination GUID policy check
9f231a8 libvendor/osm_vendor_ibumad.c: fix mad validation in case of multipath
record response
2be4e20 opensm/perfmgr: update node name when Node Description is received
from node
f37bf3c opensm/perfmgr: skip data counters when only printing errors
8412591 opensm/perfmgr: mark/report time of last counter update

4.2 Other Bug Fixes

580e6d8 Remove redundant negativity check of size_t type, which is unsigned thus non negative.
086c870 libopensm/osm_helper.c: Fix printing trap 256 details
8870b89 libopensm/osm_helper.c: Fix printing trap 259 details
9b3e860 osm_link_mgr.c: Fix checking if port support link width 2x
0a8d939 osm_sa_mcmember_record.c: Allow MCMR requests with default subnet prefix
9b9ea72 Fix spelling mistake of "switches"
70722b4 ib_types: Drop packed attribute where unnecessary
9a7cc0d opensm.spec.in: Move COPYING back into doc
b2f10ec opensm.spec.in: Updated for move to github
8bf41a5 travis: Add patch check
2e1bd5e Add travis validatio
393f665 osm_[port ucast_ftree].c: Remove unused static functions
3bcefae osm_ucast_ftree.c: Fix clang warning about empty loop
992687f osm_opensm.c: Fix use of enum as NULL pointer in osm_opensm_init_finish
1c57b7d libvendor/osm_vendor_ibumad.c: Fix type of array passed to umad_get_ca_portguids in libibumad
f5c21ce osmtest.c: Remove ununsed osmtest_get_node_rec routine
e3e49a9 osmtest/main.c: Fix show_usage declaration
ef67b6a osmtest: Add missing static keywords
2989b31 libvendor,osmtest: Use NULL instead of 0 in all places where it used as a pointer
9b8bcc1 osmtest/main.c: Fix return type for getopt_long_only
8e6d76c osm_[congestion_control perfmgr].c: Fix signed vs unsigned comparison
476b822 osm_opensm.c: Fix static declaration
6d49a7e libvendor/osm_vendor_mlx_sim.c: In osmv_transport_init, fix memory leaks on error
e76b7ab libvendor/osm_vendor_mlx[_hca]_sim.c: Eliminate use of exit
d34622f gen_chlog.sh: Update script to use git describe rather than git cat-file tag
92a9c3e osm_[link_mgr trap_rcv].c: Check the return value of osm_get_port_by_guid
06b168a main.c: Remove NO_EFFECT code
20c135d osm_helper.c: Make "50" string proper fixed width in lsea_str_fixed_width
df22e54 osm_helper.c: Fix lsea_str_fixed_width OVERRUN issue
d289316 osm_trap_rcv.c: Fix missing log message when 10 traps are received from the same source within time window
ceef038 man/opensm.8.in: Quiet some man warnings
aae42c6 Fix various typos
8e6abe9 Fix typo in OSM_DEFAULT_TRAP_SUPPRESSION_TIMEOUT
6b71ec0 Fix a bunch of typos
7c2dd40 osm_opensm.c: No need to check context for default routing engine in destroy_routing_engines
7f98069 osm_opensm.c: destroy_routing_engines should destroy the default routing engine
a66a09c osm_subnet.c: Free per_module_logging_file in subn_opt_destroy
7c9521b osm_sa.c: Remove unneeded label in osm_sa_bind
e000a2e osm_resp.c: No need to swap DR [D/S]LIDs in resp_make_resp_smp
67799fa opensm/osm_service.h: Fix and add some comments
5da75d8 opensm/osm_mlnx_ext_port_info_rcv.c: Check the pointer of osm_sm_t before accessing it
406dc70 opensm/osm_mtree.h: Improve comment
7b06728 opensm/osm_remote_sm.c: Improve comment
d817750 opensm/osm_ucast_cache.h: Improve coding style and comments
717312e opensm/osm_ucast_mgr.h: Add comment for 'max_lid' foeld in osm_ucast_mgr structure
718c14b opensm/osm_multicast.h: Improve comments
f538e69 opensm/osm_sa_mad_ctrl.h: Improve comments
6173c97 opensm/osm_sm_mad_ctrl.h: Improve comments
2414219 opensm/osm_remote_sm.h: Improve comments
6f2cbdb opensm/osm_mtree.h: Improve comments
242f0a8 opensm/osm_vl15intf.h: Minor update of comments
a989601 opensm/osm_node.h: Improve comments
519c2d1 opensm/osm_router.h: Improve comments
537cbb9 Delete unused header opensm/osm_attrib_req.h
688543e opensm/osm_path.h: Delete comments for non-existent struct field and function parameters
4974027 Revert complib/cl_event_wheel.c: Fix memory leak in event_wheel mechanism
f911f13 opensm/osm_base.h: Delete comments about non-existent "Base" class
fb9c811 opensm/osm_base.h: Delete unused header complib/cl_types.h
e316e0a complib/cl_event_wheel.c: Minor update to the sample test program
ae1c005 complib/cl_event_wheel.h: Improve comment documentation
1b1048b complib/cl_[dispatcher event_wheel].h: Fix commentary typo
c11b31c complib/cl_event_wheel.c: Handle malloc failure in cl_event_wheel_reg
7345086 complib/cl_event_wheel.h: Eliminate unneeded field in cl_event_wheel_reg_info_t
be122bc complib/cl_event_wheel.c: Some cosmetic changes
44afd73 complib/cl_event_wheel.c: Fix memory leak in event_wheel mechanism
12c24d1 complib/cl_dispatcher.h: Improve comments
b69dc33 complib/cl_dispatcher.c: Fix typo and delete one incorrect comment
c558360 include/opensm: Remove some redundant includes
169b144 include/complib: Delete documentation about 'p_nil'
2c6c6b4 complib/cl_debug.h: Cosmetic formatting changes to some macros
8a4172c complib/cl_heap.c: Remove redundant initialization statement
b42bea6 complib/cl_heap.h: Replace 'shift_' with 'heap_' in the DESCRIPTION section
5446e32 complib/cl_qcomppool.h: Improve max_objects comment
d429d5f include/complib: Fix comments and documentation
0877d07 complib/cl_ptr_vector.h: Fixed cut 'n paste error in cl_ptr_vector_remove NOTES
329cb4a complib/cl_types.h: Cosmetic formatting changes
250204d osm_db_files.c: Remove an obsolete GUID-length check on osm_db_restore
15c1acc osm_sa_mcmember_record.c: Change level of log messages for port GID subnet prefix mismatches in join/leave
2b9cf6f Use precision specifier for scanf
43270a6 Replace hard coded constants with defines for force_link_xxx options
901af61 osm_helper.h: Remove trailing whitespace in comment
a0aee8b Revert osm_db_files.c: Fix bad free in osm_db_delete
9dc1106 osm_prtn_config.c: Fix pointer dereference in verify_val
c9fcdfd osm_prtn_config.c: Cosmetic formatting change
024fe73 opensm.8.in:  Emphasize that the fields of mgroup_flag must be split with "comma"
1f82c22 partition-config.txt: Emphasize that the fields of mgroup_flag must be split with "comma"
04d2a8b osm_prtn_config.c: parse_group_flag log suspicious group flag value
bfa7f34 osm_sm_mad_ctrl.c: Change [i r]path declarations in log_rcv_cb_error
a07b48a osm_console.c: Fix resource leak in dump_portguid_parse
9da76ca osm_qos_parser_y.y: Fix opensm crash when qos policy file is null
a16cdef osmtest.c: Close file before exit function osmtest_create_inventory_file
87848a2 osmtest.c: osmtest_parse_path fix resource leak in error path
a3e2286 osmtest.c: osmtest_parse_port fix resource leak in error path
f03f013 osmtest.c: osmtest_parse_node delete p_node in error path
b8048cd osmt_service.c: Fix resource leak in osmt_get_all_services_and_check_names
b10f46c osm_ucast_nue.c: Fix resource leak in nue_create_context
a7084c9 osm_qos_parser_y.y: Fix resource leak
92059c2 osm_port.c: Fix memory leak in osm_port_new
dcfeb90 main.c: Close fd before return to avoid resource leak
91f0e83 opensm.spec.in: Adjust BuildRequires for covscan
2ef378f osm_ucast_lash.c: Fix comment in init_lash_structures
94bfbdb osm_sa_mcmember_record.c: Fix use after free in mcmr_rcv_join_mgrp
257d6c0 osm_mesh.c: Improve one line of comment in make_geometry
4123382 osm_db_files.c: Fix bad free in osm_db_delete
16c35ba osm_db_files.c: Cosmetic change to comment in osm_db_update
2028be0 osm_sm_mad_ctrl.c: Reduce path buffer sizes to avoid format truncation in log_rcv_cb_error
9070d50 osm_prtn.c: Avoid potential no NUL-terminated strncpy in osm_prtn_new
1c586e9 osm_console_io.c: Avoid potential no NUL-terminated strncpy in osm_console_init
f211bbc osm_sa_mcmember_record.c: Fix maybe uninitialized issue in mcmr_by_comp_mask
2f21624 Replace deprecated 'BSD_SOURCE' macro in Makefiles with GNU_SOURCE
a3a6876 Refactor common SA path record rate code into ib_path_rate_2x_hdr_fixups
7cbfac9 osm_sa_[multi]path_record.c: Add some comments for better code clarity
f096ee5 osm_sa_path_record.c: Cosmetic formatting change
16f441c osm_subnet.c: Remove redundant Bull device ID in is_mlnx_ext_port_info_supported
10cf3e0 Revert osm_vendor_ibumad.c: OpenSM no longer works with ibsim with latest libibuma
62e0ee0 osm_pkey.c: Fix comment in match_pkey
c4770cf osm_vendor_ibumad.c: OpenSM no longer works with ibsim with latest libibumad
ef0fcce osm_sa_[multi]path_record.c: Remove some redundant code in [m]pr_pr_rcv_get_path_parms
7122495 osm_[multi]path_record.c: Fix a couple of edge cases with new 2x/HDR SA rates
aa638d6 PKEY: Add functionality to ignore existing pkey indexes
9094a58 osm_subnet.c: Make formatting consistent in generated opensm.conf
ad7d56d doc/QoS_management_in_OpenSM.txt: Fix typo
a166aca osm_ucast_dfsssp.c: Uniquify some error codes
9012a8a complib/cl_heap.[h c]: Fix a corner case in d-ary heap
3d22440 osm_helper.c: Add decode of HDR supported to dbg_get_capabilities2_str
b31ec5e [current-routing.txt man/opensm.8.in]: Some minor fixups
d12e40e osm_subnet.c: Fix typo in generated configuration/options file
0d58e55 ib_types.h: mcast_pkey_trap_suppr in PortInfo attribute is 2 bits in IBA 1.3
ece32f3 osm_sa.c: Cosmetic change to 4C05 error log message
78f262a osm_qos.c: Better handling of VL arbitration tables when there is 1 data VL
a3f0c5f osm_prtn_config.c: Fix a couple of compile warnings with more recent gcc
e9ad4af osmtest/osmt_multicast.c: Fix MC join with unrealistic rate
55823ac osm_multicast.h: Fix some osm_mgrp_box structure field descriptions
7e00315 osm_sa_path_record.c: Check input parameters in osm_get_path_params
dfaf5db osm_switch.c: Fix commentary typo
b879f98 osm_sa_inform_info.c: Use defines rather than hard coded constants in infr_rcv_process_set_method
135ad4b ib_types.h: Fix bit for IB_PM_IS_ADDL_PORT_CTRS_EXT_SUP
a161ec4 ib_types.h: Replace hard coded constant with define
fe1cf8d ib_types.h: Fix some typos associated with IB_CLASS_RESP_TIME_MASK
e16fedf osm_subnet.c: Indicate that subnet prefix can't be changed at runtime
c9a3c6f osm_sa_mad_ctrl.c: It's report response rather than repress
075fbd6 osm_subnet.c: EOL missing in error message in opts_strtoull
cb99d8d gen_ver.sh: Change configure.in to configure.ac in comment
519b24d configure.ac: Update configure.in to configure.ac
1ed0eea configure.in: Update AM_INIT_AUTOMAKE to use subdir-objects
e4f6b79 Makefile.am: Fix INCLUDES warnings
b4f5374 osm_[link lid]_mgr.c: Simplify error threshold comparisons
ed4e65a osm_link_mgr.c: Simplify some link speed related comparisons
eaa1469 osm_[link lid]_mgr.c: Simplify link width comparisons
5baa2a4 osm_sminfo_rcv.c: Use initial rather than return path in smi_rcv_process_get_response
f449e50 osm_switch.h: Fix commentary typo
063154d osm_sa_mcmember_record.c: Add MGID to 1B13 error message
68db71c osm_ucast_ftree.c: Remove redundant condition in fabric_route_downgoing_by_going_up
b25e518 osm_console.c: Remove redundant condition in __get_stats
3d665b3 osm_ucast_ftree.c: Implement atomic update operation for dump file
b439c42 Fix various typos
6a1624f osm_state_mgr.c: Move subnet up event to occur after mkey related files are written
58884b5 osm_torus.c: Cosmetic formatting change
bb15d2f osm_service.c: Fix missing endian conversion in log message
c0e8141 Fix various coverity issues
a0d9157 osm_subnet.c: Add guid_routing_order_no_scatter option to opensm.conf
5d521c1 osm_state_mgr.c: Update comment in state_mgr_check_tbl_consistency
ccefa3d osm_sm_mad_ctrl.c: Add ':' to "ERR 3120" error message
dc45fca ib_types.h: Comment change to indicate 1.3.1 rather than 1.2 IBA spec
37e1246 ib_types.h: Cosmetic formatting changes
73fd8ee man/opensm.8.in: Fix typo (missing close parenthesis)
c32b813 doc/current-routing.txt: Fix typo
c4691e7 ib_types.h: Cosmetic commentary fix
85e2214 osm_sa_mcmember_record.c: Prevent log errors swamp in MC query scenario
88626f4 osm_sa_mcmember_record.c: Updated Mellanox copyright years
432727b osm_sa_mcmember_record.c: Cosmetic formatting change to mcmr_rcv_create_new_mgrp
b85c31e osm_prtn_config.c: Cosmetic formatting changes in manage_membership_change
1a2a6d7 osm_prtn_config.c: Updated Mellanox copyright years
8b288b8 osm_mcast_mgr.c: Cosmetic formatting change in create_mgrp_switch_map
4772806 osm_state_mgr.c: Fix uninitialized area of SMInfo SET mad
175de41 osm_prtn.c: Cosmetic formatting change
870b21a osm_ucast_dfsssp.c: Fix some commentary typos
d5c51e9 osm_ucast_mgr.c: Cosmetic formatting change
f234d83 osm_inform.c: Cosmetic code refactoring in match_inf_rec
fdd1a68 Updated some Mellanox copyrights
ecf07eb Set Type field in Notice attribute using IB_NOTICE_TYPE_SUBN_MGMT define
022e6af Use SM trap defines from ib_types.h rather than hard coded values
d111a2c ib_types.h: Cosmetic formatting change
1cbea4b osmtest/osmt_multicast.c: Cosmetic changes
c52a1b2 osm_drop_mgr.c: Eliminated redundant check for switch node type
2c4771f osm_ucast_dfsssp.c: Minor change to setting dropped when switch exists
e94fc29 osm_subnet.[h c]: Fix wrong function documentation and parameter list
098259e Updated Mellanox copyrights in some recently changed files
1f3dcb4 osm_ucast_ftree.c: Cosmetic formatting changes in fabric_construct_hca_ports
e92cfe5 iba/ib_types.h: Fix commentary typo
5109f44 osm_ucast_ftree.c: Cosmetic formatting change
0bc858e osm_ucast_ftree.c: Cosmetic variable name changes in ftree_port_group_t struct
08e8a9a osm_sa_path_record.c: Refactored PR [D/S] GID and LID validation
7777987 osm_sa_path_record.c: Cosmetic formatting change
a7ac5fc osm_prtn_config.c: Handle valgrind warning in osm_prtn_config_parse_file
609b777 osm_ucast_ftree.c: Cosmetic formatting change
d9a601b osm_ucast_ftree.c: Remove no longer needed code in remove_depended_hca
1f6cd87 osm_state_mgr.c: Cosmetic formatting changes
7f2f902 libvendor/osm_vendor_ibumad_sa.c: Cosmetic formatting change to ERR
5501 log message
e527ec6 libvendor/osm_vendor_ibumad_sa.c: Cosmetic formatting changes to
osmv_query_sa
ec2e2bf osm_pkey.h: Fix osm_physp_has_pkey method description
bf3818b partition_config.txt: Small correction in doc file
9fa761c osm_ucast_ftree.c: Removed *p_ftree parameter from sw_destroy function
652b063 osm_ucast_ftree.c: Removed *p_ftree parameter from sw_create function
d504e76 doc/QoS_management_in_OpenSM.txt: Cosmetic changes
a28d63e Fix documentation on ignore-guids command line option
df32644 osm_subnet.c: Minor clarification to SwitchCongestionSetting Control
Map description in generated option file
483ff00 man/osmtest.8: Minor tweaks to inventory option description
8d3c6f8 osm_sa_mcmember_record.c: Change to log messages for PKey consistency
504e43f osm_req.c: Cosmetic formatting change
a5e484b osm_node.h: Fix commentary typo
6ceb0ce osm_sa_mcmember_record.c: Removed redundancy in comment
9e0cba3 osm_prtn_config.c: Cosmetic formatting change
0eb5117 osm_sa_mcmember_record.c: Cosmetic formatting change
2541ed9 osm_ucast_mgr.c: Cosmetic formatting change
28fd4ee osm_[subnet congestion_control].c: Cosmetic commentary change
391c244 osm_congestion_control.c: Added Mellanox copyright
7e08621 osm_congestion_control.c: In cc_rcv_mad, add attribute ID and modifier
to log message
62075d6 osm_perfmgr.c: Fix endian of MAD status in pc_recv_process
cd94c9c osm_congestion_control.c: Fix endian of MAD status in cc_rcv_mad
cb5df8b osm_congestion_control.c: Fix endian of node and port GUIDs in some
log messages
68887fb osm_congestion_control.c: Cosmetic changes
1331a46 osm_subnet.[h c]: Fix possibility for open file descriptor issue
ce15bb7 osm_perfmgr.c: Output remote port on perfmgr error counter log
messages
030ac82 osm_sa.c: Improve ERR 4C05 log message
fbb63d6 osm_subnet.c: Update MEPI supported devices white list
0774229 osm_subnet.c: Cosmetic formatting change
5871ef7 osm_sa.c: Check return value from chmod in opensm_dump_to_file
61cd0cf osm_switch.c: Fix potential memory leak due to misuse of realloc
c78adde osm_req.c: Initialize dest_port_guid in req_determine_mkey
dd5e5df osm_mcast_mgr.c: Add MLID to error 0A06 log message
63900bc osm_vendor_ibumad.h: Use UMAD_MAX_DEVICES for OSM_UMAD_MAX_CAS
a5c0200 iba/ib_types.h: Add support for new MAD SM:PortInfoExtended and for
modifying PM:PortExtendedSpeedsCounters
3f99535 osm_port_info_rcv.c: In osm_pi_rcv_process, move assert before first
log message
a0b9444 Change osm_subn_t.log_max_size type to uint32_t
b5abaa2 osmeventplugin.c: Add include of osm_config.h as first OpenSM include
ecce4e3 osm_console.c: Handle LinkSpeed[Ext]Active 0 for portstatus command
f622810 osm_console.c: Fix unknown speed/width port reporting for portstatus
command
ffdd042 osm_console_io.c: In is_authorized, STRING_UNKNOWN is define
a6ba888 Add missing keyword in partition definition in man page and
partition.txt doc
7444e5e Fix the difference in osm_opensm struct size between OpenSM and
plugin(s)
caf764f osm_subnet.h: Fixed commentary typo
117bc82 osm_prtn.c: Change message verbosity for log message in
osm_prtn_add_port
3b172ab osm_config.h.in: Fixed the difference in osm_opensm_t struct size
between opensm and plugins
d13d4bd osm_console.c: Track and report unknown/speed width ports
d63e7f6 osm_sa_path_record.c: Cosmetic formatting changes
48d159b libvendor/osm_vendor_ibumad.c: Remove GID index 0 check in
umad_receiver
d7f1d02 libvendor/osm_vendor_ibumad.c: memset osm_mad_addr_t before setting
fields
96f89d6 osm_console.c: Included unknown speed/width ports in "possible issues"
44df235 osm_dump.c: In dump_topology_node, handle link_width_active of 4 for
8X
a1e58e0 osm_link_mgr.c: Fix bug in mlnx extended port info setting
fcb6967 osm_subnet.c: Improve sweep_on_trap documentation in generated conf
file
47a6b00 osm_sa_multipath_record.c: Better logging for 4514, 4515, and 4505
error messages
115529b osm_sa_path_record.c: Better logging for 1F02 error message
fe079df osm_sa_path_record.c: Better logging for 1F05 and 1F03 error messages
43c378f osm_ucast_mgr.c: A couple of cosmetic log message changes in
ucast_mgr_route
9a5e514 osmtest/osmtest.c: Cosmetic formatting change
90db6c3 osm_state_mgr.c: Cosmetic formatting changes
e7139af osmtest/osmtest.c: Fix osmtest_get_sm_gid when running osmtest on node
other than SM node
83ea812 doc/performance-manager-HOWTO.txt: Update perfmgr config options
documentation
d4d5af3 libvendor/osm_vendor_ibumad_sa.c: Fixed endian in debug log message
c1080b9 osmtest/main.c: Cosmetic change to output for consistency
179fbac Add support for additional Mellanox OUI
be3e4f1 osm_subnet.c: Remove duplicate strcmp check in
osm_subn_rescan_conf_files method
e6fc2f8 Add some missing documentation files to installation
ed1571a osm_subnet.c: Change default for perfmgr_query_cpi option
3a1b458 osm_mcast_mgr.c: Cosmetic change to error log mesage
56bd964 opensm.init.in: Fix return value checking in opensm.init script
787c16e Eliminate redundant calls to ib_port_info_compute_rate
6a2d081 Shorten long lines while calculating SA rate
6ec10d6 man/opensm.8.in: Update date on man page
1dcf322 Improve scatter ports documentation
b1a8fb0 osm_subnet.c: Cosmetic formatting change in subn_validate_neighbor
d1b70fc osm_perfmgr.c: Add log message when perfmgr sweep is skipped
8098a4d osm_perfmgr.c: Remove unnecessary log message
38bc2e4 Skip state_mgr_check_tbl_consistency when no LID is changed by lid or
link managers
ce73c60 osm_drop_mgr.c: Add missing CR at end of log message in
drop_mgr_check_node
8255f8f osm_state_mgr.c: Improve error flow with wrong LIDs
86f1720 osm_sa_path_record.c: Fix some commentary typos
2bc6074 osmtest/main.c: Output formatting change for case 'x'
60727f7 osmtest: Add GRH tests for SA queries
d29dcd7 osm_sa_path_record.c: Fix misleading error messages during sweep
e91908b Add osm_congestion_control.c to per module logging support
3de1091 osm_congestion_control.c: Fix error code
3ee0a22 osm_sm.c: In sm_sweeper, no need to check for timeout after
cl_event_wait_on EVENT_NO_TIMEOUT
f3e1924 osm_trap_rcv.c: In shutup_noisy_port, improve ERR 3811 log message
f73ff43 osm_mcast_mgr.c: In mcast_mgr_process_mlid, cosmetic change to log
message
ab9fec9 osm_congestion_control.c: In cc_poller_send, handle cl_event_wait_on
return status
af04af7 osm_[sm sa]_mad_ctrl.c: Improve unsupported attribute error messages
16fee25 osm_helper.c: In osm_get_lsa_str, fix printing of wrong FDR10 data if
link is down
6c70bf1 PerfMgr: Eliminate no longer used sig_sweep variable
41d0a42 osm_perfmgr.c: In perfmgr_send_mad, handle cl_event_wait_on return
status
d764777 osm_perfmgr.c: Minor code factoring in perfmgr_send_mad
fa31298 doc/opensm_release_notes-3.3.txt: Updated repo location
8394ef8 libvendor/osm_vendor_ibumad.c: Commentary change
0ab7492 osm_subnet.c: More cosmetic changes to opensm conf file PerfMgr
documentation
cc977be osm_subnet.c: Enhance opensm conf file documentation for PerfMgr
options
38273db osm_port_info_rcv.c: Fixed calculation of min_data_vls
dc3259e Add support for additional Mellanox OUI
99a8e74 osmtest.c: Fixed missing assignment of return value from function
osmtest_get_port_rec_by_num
9ffa520 Fix the creation of empty multicast groups from SADB
1d3aacf osm_perfmgr_db.c: Add missing clear of new xmit_wait counter in
clear_counters
0f9b15c SM should resweep the fabric if vl15_send_mad fails
ee5f6d5 osm_perfmgr.c: Added Mellanox copyright
05be6c4 osm_perfmgr.c: Eliminate unneeded initialization in pc_recv_process
9ac71fd osm_perfmgr.c: Cosmetic formatting changes
54c6c86 osm_log.c: Fix wrong hour and date display in log when CL_ASSERT fail
5c81051 osm_console_io.c: Handle return value of function setsockopt
2e1294a osm_sa_[mcmember path]_record.c: Optimize clearing of SA record items
db9c450 osm_guid_info_rcv.c: Fix assert placement in osm_gi_rcv_process
bb723ae libvendor/osm_vendor_ibumad.c: Cosmetic change to umad_set_grh calls
5ca6bdc osm_perfmgr.c: Add current PerfMgr sweep state to 54FF error log
message
753af81 osm_mcast_mgr.c: Add missing new line at end of ERR 0A21 log message
d437d58 osm_mcast_mgr.c: Fix endian of port GUID in ERR 0A06 log message
51fb51d osm_ucast_mgr.c: Fix duplicated error codes
21c2ab7 osm_sa_mad_ctrl.c: In sa_mad_ctrl_rcv_callback, improve 1A04 error log
message
c83bde4 doc/performance-manager-HOWTO.txt: Fix typo
abaf91b osm_sa_service_record.c: Improved locking
1f4de58 osm_subnet.c: Fix bug in parsing configuration file
0fa5fc1 osm_sa_mcmember_record.c: Fix removing members from existing mc group
due to invalid requests
e156626 osm_qos_parser_y.y: Added range check for mtu limit parsing
cb439b3 osm_qos_parser_y.y: Added range check for rate limit parsing
a556f82 man/opensm.8.in: Minor fixes to per module logging configuration
7991745 osm_node_info_rcv.c: Update local copy of node info for known nodes
1c637df osm_node_info_rcv.c: Update NodeInfo.SysImageGUID on heavy sweep
275a56a osm_state_mgr.c: Clear first time sweep even after subnet error
09b5ffe osm_sa_path_record.c: In osm_pr_rcv_process, release lock before log
message
ef7a651 osm_state_mgr.c: Revert commit to "remove redundant unset to
first_time_master_sweep"
23dfbf8 osm_state_mgr.c: Remove redundant unset to first_time_master_sweep
e5a87dd Revert "Reset client reregistration when receiving handover"
28e5fa7 osm_sw_info_rcv.c: Add check of switch mcast_cap
78b6e8f osm_subnet.c: Fix resource leak neighbor parser
("subn_validate_neighbor" function)
46749c0 osm_subnet.c: Fix resource leak guid2mkey parser (guid validation
function)
6212e4b osm_ucast_ftree.c: Fix memory leak in ftree fabric_rank
afb6cb8 osm_ucast_mgr.c: Use LFT block of all port 0s to indicate resend
319e065 ib_types.h: Rename ib_switch_info_set_state_change function
b1c17a8 ib_types.h: Fix shadow declaration warnings
e3f0440 osm_ucast_updn.c: Fix the AA0B error number
30d9020 osm_ucast_updn.c: Add missing ERR number to log message
e8a9275 osm_port.c: Improve ERR 4108 log message
740c22b opensm/include/opensm/osm_log.h: Fix commentary cut 'n paste error
13ebee4 opensm/osm_subnet.c: Fixed ftree/updn configuration failure when
	root_guid_file points to non-existing file
8a9d267 opensm: fix locking in osm_guid_mgr_process
4d682bb opensm: Fix pthread_create() return value checks
63c6609 opensm/osm_port.h: Fix commentary typo
3e4e00b opensm/osmtest: fix osmtest ignores timeout parameter
68b1d92 opensm: perfmgr fix dump_counters
9d16039 opensm/perfmgr: fix endian conversion of PortCounters
324f269 opensm/osm_sa_inform_info.c: Fix some error log messages
1d5213a opensm/osm_madw.h: Fix a couple of cut 'n paste commentary errors
3fc662d opensm/torus-2QoS: Fix some typos in documentation
048c66e Fixed Multicast precreation parsing
086d611 Fixes in SL2VL table distribution algorithm
7d9f0c9 Fix deadlock between sminfo_set_req() and osm_sm_state_mgr()
69741e6 Fix base port0 sl2vl mapping optimization
bcda38e Fix SL2VL configuration
7e39542 Fix pre-creation of MC group with MGID containing P_Key
20e1a46 opensm/osm_congestion_control.c: Fix initialization hex string
a59072d opensm/osm_congestion_control.c: Skip TID 0 on 32 bit wraparound
7d18662 osmtest/osmtest.c: Fix permission
e7d4574 opensm/perfmgr: update new error codes to '54' prefix
e17dae6 opensm/osm_sa_portinfo_record.c: In pir_rcv_new_pir, fix switch port 0 physp
93b2f56 libvendor/osm_vendor_ibumad.c: fix unused-but-set warning
cda58af fixed unsused-but-set warning for DEBUG variables
e881d0e opensm/osmtest/osmt_multicast.: Fix typo in log message
203f3c6 opensm/osm_switch.c: Fix compile warnings
67c2538 opensm/man/opensm.8.in: Fix some typos
7338efc opensm/osmtest/osmtest.c: Fix endian in some log messages
67063ca opensm/libvendor: Fix compile warnings on 64 bit machines when building --with-osmv=sim
a8c209c opensm: fixed port order configuration in torus routing engine
7359cfc opensm/osm_ucast_mgr.c: Fix some issues found by Coverity
0150ab9 opensm/osm_ucast_ftree.c: Fix some issues found by Coverity
267a08f opensm/osm_pkey_mgr.c: Fix cast
12f772d opensm: Fix some OSM_SIGNAL and OSM_SM_SIGNAL numbering
c29c4f1 opensm/osm_sa_service_record.c: Fix minor memory leak
f936f8b opensm: fix strtoull error handling
0292ae2 opensm: fixed segfault when enable qos on fabric with no switches
619fa64 osmtest/osmt_multicast.c: Fixed another insufficient component case
1618803 opensm/osm_sa.c: Fix commentary typo
c98fec9 opensm/osm_subnet.c: Fix description of max_msg_fifo_timeout
0a3839f osmtest/osmtest.c: Fix trap flow not implemented log message
f08479a opensm/osm_base.h: Fix a commentary typo
ae966f6 osmtest/osmt_multicast.c: Fix some typos
8a3b5b9 osmtest/osmt_multicast.c: Fixed some error codes in OSM_LOG messages
c90953d osmtest/osmt_multicast.c: Fixed a couple of typos in OSM_LOG messages
f7f1ead opensm/osm_sa_mcmember_record.c: Fix handling of invalid PKey
65d3e4f opensm/osm_pkey_mgr.c: Fix commentary typo
34d61cc opensm/perfmgr: fix overflow processing
77d79b4 opensm: fixed potential null variable dereferencing in libvendor
350c6e4 opensm: fixed potential memory leak in osm_ucast_ftree()
e206872 opensm: Fixed debug message in osm_vendor_send()
1b3e93e opensm: fixed sizeof of pointer allocation in osm_ucast_lash()
f0b915a Fix SANodeRecord.nodeInfo.localPortNum
3332658 opensm: fixed memory leak in multicast spanning tree calculation
e4525b1 opensm: fixed indentation and decreased verbosity of RMPP length message
10ac4c1 Fix compile warning introduced by patch "fixed getline pointer allocation free in osm_console_io"
bf23d7c opensm: fixed getline pointer allocation free in osm_console_io
54b1583 Makefile: ChangeLog and version generation script path fix
4911e0b performance-manager-HOWTO.txt: Indicate master state
86ccaa4 opensm/osm_pkey_mgr.c: Fix pkey endian in log message
b79b079 opensm.8.in: Add mention of backing documentation for QoS policy
	file and performance manager
b4d92af opensm/osm_perfmgr.c: Eliminate duplicated error number
a10b57a opensm/osm_ucast_ftree.c: lids are always handled in host order
44273a2 opensm/osm_ucast_ftree.c: fixing bug in indexing
5cd98f7 Fix further bugs around console closure and clean up code.
6b34339 opensm/osm_opensm.c: add newline to log message
68c241c send trap144 when local priority is higher than master priority
6462999 opensm/osm_inform.c: In __osm_send_report, make sure p_report_madw
	valid before using
9b8561a opensm/console: Fixed osm_console poll to handle POLLHUP
91d0700 osm_vendor_ibumad.c: In clear_madw, fix tid endian in message
5a5136b osm_switch.h : Fixed wrong comment about return value of
	osm_switch_set_hops
c1ec8c0 osm_ucast_ftree.c: Removed useless initialization on switch indexes
418d01f opensm/osm_helper.c: use single buffer in osm_dump_dr_smp()
2c9153c opensm/osm_helper.c: consolidate dr path printing code
048c447 opensm/osm_helper.c: return then log is inactive
dd3ef0c opensm: Return error status when cl_disp_register fails
0143bf7 opensm/osm_perfmgr.c: Improve assert in osm_pc_rcv_process
6622504 osm_perfmgr.c: In osm_perfmgr_shutdown, add missing cl_disp_unregister
7b66dee opensm: remove unneeded anymore physp initializations
f11274a opensm/partition-config.txt: Update for defmember feature
d240e7d opensm/osm_sm_state_mgr.c: Remove unneeded return statement
898fb8c opensm: Improve some snprintf uses
6820e63 opensm/osm_sa_link_record.c: improve get_base_lid()
64c8d31 opensm: initialize all switch ports
555fae8 opensm/sweep: add log message before lid assignment
8e22307 opensm/console: Enhance perfmgr print_counters for better nodenames
b9721a1 opensm/osm_console.c: Improve perfmgr print_counters error message
4d8dc72 opensm/osm_inform.c: Fix sense of zero GID compare in __match_inf_rec
a98dd82 opensm/main.c: remove enable_stack_dump() call
db6d51e opensm/osm_subnet: fix crash in qos string config parameters reloading
e5111c8 opensm: proper config file rescan
e5295b2 opensm: pre-scan command line for config file option
e2f549e opensm/osm_console.c: Eliminate some extraneous parentheses
0a265dc opensm/console: dump_portguid - don't duplicate matched guids
540fefb opensm/console: dump_portguid command fixes
d96202c opensm/osm_console.c: Add missing command in help_perfmgr
ae1bd3c opensm/osm_helper.c: Add port counters to __osm_disp_msg_str
1d38b31 opensm/osm_ucast_mgr.c: Add error numbers for some OSM_LOG prin
156c749 opensm: fix structure definition for trap 257-258
5c09f4a opensm/osm_state_mgr.c: small bug in scanning lid table
72a2fa2 opensm/osm_sa.c: fixing SA MAD dump
539a4d3 opensm/osm_ucast_ftree.c Fixed bad init value for down port index
6690833 opensm/ftree: simplify root guids setup.
90e3291 opensm/ftree: cleanup ftree_sw_tbl_element_t use
c07d245 opensm/qos_config: no invalid option message on default values
b382ad8 opensm: avoid memory leaks on config parameters reloading
45f57ce opensm/osm_ucast_ftree.c: Fixed bug on index port incrementation
3d618aa opensm/osm_subnet.c: break matching when config parameter already found
44d98e3 opensm/osm_subnet.c: clean_val() remove trailing quotation
173010a opensm/doc/perf-manager-arch.txt: Fix some commentary typos
83bf6c5 opensm/osm_subnet.c fix parse functions for big endian machines
6b9a1e9 opensm/PerfMgr: Primarily fix enhanced switch port 0 perf manager
	operation
4f79a17 opensm/osm_perfmgr.c: In osm_perfmgr_init, eliminate memory leak
	on error
22da81f opensm/osm_ucast_ftree.c: fix full topology dump
aa25fcb opensm/osm_port_info_rcv.c: don't clear sw->need_update if port 0
	is active
003bd4b opensm/osm_subnet.c Fix memory leak for QOS string parameters.
9cbbab2 opensm/opensm.spec: fix event plugin config options
996e8f6 OpenSM: update osmeventplugin example for the new TRAP event.
67f4c07 opensm/lash: simplify some memory allocations
3e6bcdb opensm/lash: fix memory leaks
3ff97b9 opensm/vendor: save some stack memory
ccc7621 opensm/osm_ucast_ftree.c: fixing errors in comments
1a802b3 Corrected incoherency in __osm_ftree_fabric_route_to_non_cns comments
85a7e54 opensm/osm_sm.c: fix MC group creation in race condition
aad1af2 opensm/osm_trap_rcv.c: Improvements in log_trap_info()
f619d67 opensm/osm_trap_rcv.c: Minor reorganization of trap_rcv_process_request
084335b opensm/link_mgr: verify port's lid
d525931 opensm/osm_vendor_ibumad: Use OSM_UMAD_MAX_AGENTS rather than
	UMAD_CA_MAX_AGENTS
f342c62 opensm/osm_sa.c: don't ignore failure in osm_mgrp_add_port()
587fda4 osmtest/osmt_multicast.c: fix strict aliasing breakage warning
6931f3e opensm: make subnet's max mlid update implementation independent
30f1acd osm_ucast_ftree.c missing reset of ca_ports
ac04779 opensm: fix LFT allocation size
a7838d0 opensm/osm_ucast_cache: reduce OSM_LOG_INFO debug printouts
c027335 opensm/osm_ucast_updn.c: Further reduction in cas_per_sw allocation
e8ee292 opensm/opensm/osm_subnet.c: adjust buffer to ensure a '\n' is printed
84d9830 opensm/osm_ucast_updn.c: Reduce temporary allocation of cas_per_sw
347ad64 opensm/ib_types.h: Mask off client rereg bit in set_client_rereg
c2ab189 opensm/osm_state_mgr.c: in cleanup_switch() check only relevant
	LFT part
40c93d3 use transportable constant attributes
c8fa71a osmtest -code cleanup - use strncasecmp()
770704a opensm/osm_mcast_mgr.c: In mcast_mgr_set_mft_block, fix node GUID
	in log message
3d20f82 opensm/osm_sa_path_record.c: separate router guid resolution code
27ea3c8 opensm: fix gcc-4.4.1 warnings
c88bfd3 opensm/osm_lid_mgr.c: Fix typo in OSM_LOG message
a9ea08c opensm/osm_mesh.c: Add dump_mesh routine at OSM_LOG_DEBUG level
bc2a61e C++ style coding does not compile
6647600 opensm: remove meanless 'const' keywords in APIs
323a74f opensm/osm_qos_parser_y.y: fix endless loop
0121a81 opensm: fix endless looping in mcast_mgr
696c022 opensm: fix some obvious -Wsign-compare warnings
b91e3c3 opensm/osm_get_port_by_lid(): don't bother with lmc
ca582df opensm/osm_get_port_by_lid(): speedup a port lookup
fd846ee opensm/osm_mesh.c: simplify compare_switches() function
fe20080 osm_sa.c - void * arithmetic causes problems
220130f osm_helper.c use explicit value for struct init
0168ece use standard varargs syntax in macro OSM_LOG()
180b335 update functions to match .h prototypes
9240ef4 opensm/osm_ucast_lash: fix use after free bug
6f1a21a opensm: osm_get_port_by_lid() helper
c9e2818 opensm/osm_sa_path_record.c: validate multicast membership
225dcf5 opensm/osm_mesh.c: Remove edges in lash matrix
4dd928b opensm/osm_sa_mcmember_record.c: clean uninitialized variable use
c48f0bc opensm/osm_perfmgr_db.c: Fix memory leak of db nodes
82d3585 opensm/osm_notice.c: move logging code to separate function
9557f60 opensm/osm_inform.c: For traps 64-67, use GID from DataDetails in
	log message
e2e78d9 opensm/opensm.8.in: Indicate default rule for Default partition
08c5beb opensm/osm_sa_node_record.c: dump NodeInfo with debug verbosity
1fe88f0 opensm/multicast: merge mcm_port and mcm_info
ba75747 opensm/multicast: consolidate port addition/removing code
5e61ab8 opensm: port object reference in mcm ports list
5c5dacf opensm: fix uninitialized return value in osm_sm_mcgrp_leave()
7cfe18d osm_ucast_ftree.c: Removed reverse_hop parameters from
	fabric_route_upgoing_by_going_down
aa7fb47 opensm/multicast: kill mc group to_be_deleted flag
a4910fe opensm/osm_mcast_mgr.c: multicast routing by mlid - renaming
1d14060 opensm/multicast: remove change id tracking
5a84951 opensm: use mgrp pointer as osm_sm_mcgrp_join/leave() parameter
d8e3ff5 opensm: use mgrp pointer in port mcm_info
0631cd3 opensm doc: Indicated limited (rather than partial) partition
	membership
1010535 opensm/osm_ucast_lash.c: In lash_core, return status -1 for all errors
942e20f opensm/osm_helper.c: Add SM priority changed into trap 144 description
2372999 opensm/osm_ucast_mgr: better lft setup
e268b32 opensm/osm_helper.c: Only change method when > rather than >=
9309e8c complib/cl_event.c: change nanosec var type long
d93b126 opensm/complib: account for nsec overflow in timeout values
ef4c8ac opensm/osm_qos_policy.c: matching PR query to QoS level with pkey
c93b58b opensm: fixing some data types in osm_req_get/set
2b89177 opensm/libvendor/osm_vendor_ibumad.c: Handle umad_alloc failure in
	osm_vendor_get
2cba163 opensm/osm_helper.c: In osm_dump_dr_smp, fix endian of status
47397e3 opensm/osm_sm_mad_ctrl.c: Fix endian of status in error message
e83b7ca opensm/osm_mesh.c: Reorder switches for lash
9256239 opensm/osm_trap_rcv.c: Validate trap is 144 before checking for
	NodeDescription changed
011d9ca opensm/osm_ucast_lash.c: Handle calloc failure in generate_cdg_for_sp
59964d7 opensm: fixing handling of opt.max_wire_smps
f4e3cd0 opensm/osm_ucast_lash.c: Directly call calloc/free rather than
	create/delete_cdg
5a208bd opensm/osm_ucast_lash.c: Added error numbers to some error log messages
3b80d10 opensm/osm_helper.c: fix printing trap 258 details
f682fe0 opensm: do not configure MFTs when mcast support is disabled
cc42095 opensm/osm_sm_mad_ctrl.c: In sm_mad_ctrl_send_err_cb, indicate
	failed attribute
aebf215 opensm/osm_ucast_lash.c: Remove osm_mesh_node_delete call from
	switch_delete
1ef4694 opensm/osm_path.h: In osm_dr_path_init, only copy needed part of path
c594a2d opensm: osm_dr_path_extend can fail due to invalid hop count
46e5668 opensm/osm_lash: Fix use after free problem in osm_mesh_node_delete
81841dc opensm/osm_ucast_lash.c: Handle malloc failures better
2801203 opensm: remove extra "0x" from debug message.
88821d2 opensm/main.c: Display SMSL when specified
f814dcd opensm/osm_subnet.c: Format lash_start_vl consistent with other
	uint8 items
66669c9 opensm/main.c: Display LASH start VL when specified
31bb0a7 opensm/osm_mcst_mgr.c: check number of switches only once
75e672c opensm: find MC group by MGID using fleximap
2b7260d Clarify the syntax of the hop_weights_file
e6f0070 opensm/osm_mesh.c: Improve VL utilization
27497a0 opensm/osm_ucast_ftree.c Fix assert comparing number of CAs to CN ports
3b98131 opensm/osm_qos_policy.c: Use proper size in malloc in
	osm_qos_policy_vlarb_scope_create
e6f367d opensm/osm_ucast_ftree.c: Made error numbers unique in some log
	messages
83261a8 osm_ucast_ftree.c Count number of hops instead of calculating it
7bdf4ff opensm/osm_sa_(path multipath)_record.c: Fix typo in a couple of
	log messages
0f8ed87 opensm/osm_ucast_mgr.c: Add error numbers to some error log messages
0b5ccb4 complib/Makefile.am: prevent file duplications
e0b8ec9 opensm/osm_sminfo_rcv.c: clean type of smi_rcv_process_get_sm()
4d01005 opensm: sweep component processors return status value
6ad8d78 opensm/libvendor/osm_vendor_(ibumad mlx)_sa.c: Handle malloc
	failure in __osmv_send_sa_req
cf97ebf opensm/osm_ucast_lash.(h c): Replace memory allocation by array
957461c opensm/osm_sa.c add attribute and component mask to error message
5d339a1 osm_dump.c dump port if lft is set up
518083d osm_port.c: check if op_vls = 0 before max_op_vls comparison
b6964cb opensm/osm_port.c: Change log level of Invalid OP_VLS 0 message
	to VERBOSE
b27568c opensm/PerfMgr: Reduce host name length
bc495c0 opensm/osm_lid_mgr.c bug in opensm LID assignment
5a466fd opensm/osm_perfmgr_db.c: Remove unneeded initialization in
	perfmgr_db_print_by_name
57cf328 opensm/osm_ucast_ftree.c Increase the size of the hop table
8323cf1 opensm/PerfMgr: Remove some underbars from internal names
65b1c15 opensm: Changes to spec and make files for updated release notes
cd226c7 OpenSM: include/vendor/osm_vendor.h - Replaced #elif with no
	condition by #else
9f8bd4a management: Fixed custom_release in SPEC files
c0b8207 opensm/PerfMgr: Change redir_tbl_size to num_ports for better clarity
596bb08 opensm/osm_sa.c: check for SA DB file only if requested
2f2bd4e opensm SA DB dump/restore: load SA DB only once
4abcbf2 opensm: Added print_desc to various log messages
5e3d235 opensm/osm_vendor_ibumad.c: Move error info into single message
8e5ca10 opensm/libvendor//osm_vendor_ibumad_sa.c: uninitialized fields
d13c2b6 opensm/osm_sm_mad_ctrl.c Changes to some error messages
f79d315 opensm/osm_sm_mad_ctrl.c: Add missing call to return mad to mad pool
150a9b1 opensm/osm_sa_mcmember_record.c: print mcast join/create failures in
	VERBOSE instead of DEBUG level
9b7882a opensm/osm_vendor_ibumad.c: Change LID format to decimal in log message
5256c43 opensm/osm_vendor_mlx: fix compilation error
93db10d opensm/osm_vendor_mlx_txn.c: eliminate bunch of compilation warnings
156fdc1 opensm/osm_helper.c Log format changes
7a55434 opensm/osm_ucast_ftree.c Changed log level
a1694de opensm/osm_state_mgr.c Added more info to some error messages
fdec20a opensm/osm_trap_rcv.c: Eliminate heavy sweep on receipt of trap 145
13a32a7 opensm - standardize on a single Windows #define - take #2
b236a10 opensm/osm_db_files.c: kill useless malloc() castings
4ba0c26 opensm/osm_db_files.c: add '/' path delimited
e3b98a5 opensm/osm_sm_mad_ctrl.c: Fix qp0_mads_accounting
dbbe5b3 opensm/osm_subnet.c: fixing bug in dumping options file
f22856a opensm/osm_ucast_mgr.c: fix memory leak
0d5f0b6 opensm: osm_get_mgrp_by_mgid() helper
e3c044a osm_sa_mcmember_record.c: pass MCM Record data to mlid allocator
3dda2dc opensm/osm_sa_member_record.c: mlid independent MGID generator
1f95a3c opensm/osm_sa_mcmember_record.c: move mgid allocation code
b78add1 complib: replace intn_t types by C99 intptr_t
a864fd3 osmtest/osmt_mtl_regular_qp.c: cleaning uintn_t use
9e01318 opensm/osm_console.c: make const functions
f8c4c3e opensm/osm_mgrp_new(): add subnet db insertion
80da047 complib/fleximap: make compar callback to return int
bf7fe2d opensm: cleanup intn_t uses
0862bba opensm/main.c: opensm cannot be killed while asking for port guid
2b70193 opensm/complib: bug in cl_list_insert_array_head/tail functions
4764199 opensm - use C99 transportable data type for pointer storage
a9c326c opensm/osm_state_mgr.c: do not probe remote side of port 0
4945706 opensm/osm_mcast_mgr.c: fix return value on alloc_mfts() failures
8312a24 OpenSM: Fix unused variable compiler warning.
ab8f0a3 opensm/partition: keep multicast group pointer
a817430 opensm: Only clear SMP beyond end of PortInfo attribute
52fb6f2 opensm/osm_switch.h: Remove dead osm_switch_get_physp_ptr routine
aa6d932 opensm/osm_mcast_tbl.c: In osm_mcast_tbl_clear_mlid, use memset to
	clear port mask entry
2ad846b opensm/osm_trap_rcv.c: use source_lid and port_num for logging
b9d7756 opensm/osm_mcast_tbl: Fix size of port mask table array
11c0a9b opensm/main.c: Use strtoul rather than strtol for parsing transaction
	timeout
0608af9 opensm/osm_sm_mad_ctrl.c: In sm_mad_ctrl_send_err_cb, revert setting
	of init failure on QoS initialization failures
c6b4d4a opensm/osm_vendor_ibumad.c: Add transaction ID to osm_vendor_send
	log message
520af84 opensm/osm_sa_path_record.c: don't set dgid pointer for local subnet
4a878fb opensm/osm_mcast_mgr.c: fix osm_mcast_mgr_compute_max_hops for
	managed switch
7c48590 opensm/osm_log.c: add OSM_LOG_SYS to default flags
89f7cb6 opensm/osm_lid_mgr: use 'first_time_master_sweep' flag
0cb7fab opensm: conversion to osm_get_port_by_lid()
9d14fc0 opensm/osm_lid_mgr.c: fix memory leak
c364aa1 opensm/opensm.init.in: fix install warning on SLES11
1010c9c opensm/osm_sa_path_record.c: livelock in pr_rcv_get_path_parms
4b2cd5e opensm/vendor: fix portguids array size
52bf5b2 opensm/osm_subnet.c: fixing some options to not "hot-swappable"
8900da0 opensm/osm_subnet.{c,h}: passing options to the event plugins
051c57f Delete port only after GID OUT trap was sent
d4ebf7e opensm/complib/cl_passivelock.h: remove unneeded casting
8fdb17c opensm/complib/cl_types.h: convert cl_status_t to int
fd7fb1e opensm/osm_mcast_mgr.c: preserve root switch calculation functionality
fcb0f3a opensm/osm_mcast_mgr.c: code simplifications
444f559 opensm/osm_mcast_mgr.c: fix bug in MC root switch calculation
041ebcb opensm/osm_mcast_mgr.c: remove redundant casting
3717f53 opensm/osm_sa_pkey_record.c: optimize port selection logic
48352be opensm/osm_mcast_mgr.c: fix memory leak
f3cf83f opensm/complib/cl_ptr_vector.c: fix bug/compiler warning
27c8ebd opensm/osm_subnet.h: remove redundant function definition
f296938 opensm/osm_vl_arb_rcv.c: fix double mutex release bug
00bc48e opensm/osm_port_info_rcv.c: fix compilation warning
8823800 opensm/osm_sa.{c,h}: osm_sa_db_file_dump() return values
f4581f3 opensm/osm_qos.c: Fix typo in OSM_LOG message
e3c790a opensm/osm_update_node_desc(): minor prototype improvement
3cc68cb opensm/osm_vl_arb_rcv.c: Dump table after validating block number
7dbb96e opensm SA DB: dump only if modified
fa2106d opensm/osm_sa_infrominfo.c: fixes and simplifications in lid range check
051a1dd opensm/osm_qos.c: split switch external and end ports setup
a6c0189 opensm/osm_qos.c: merge SL2VL mapping capability check
3fe8efe opensm/osm_slvl_map_rcv.c: verify port number values received from
	network
88c372c opensm/osm_slvl_map_rcv.c: fix mutex double release bug
d282093 opensm/osm_slvl_map_rcv.c: fix port parsing on BE machine
8e9dbd3 osm_sa_path_record.c: use PR DGID by reference
7c9d375 osm_sa_path_record.c: separate mutlicast processing code
cb2d18e opensm/osm_sa_path_record.c: MGID must be specified explicitly
bd3932b opensm/osm_mcast_mgr.c: strip log-only variable
9d93de3 opensm/osm_pkey_mgr.c: Eliminate unneeded parameter from pkey_mgr_get_physp_max_blocks API
5f49472 opensm/include/osm_helper.h: Eliminate some duplicate declarations
e8ddcd4 opensm/osm_opensm.c: no report when SM is exiting
77ce7c8 complib/cl_timer: remove not needed timeval initializations
490aae2 opensm/osm_helper.c: Add some missing message names to disp_msg_str
d678a21 opensm: Modify OSM_LOG_SYS messages
4cfb481 opensm: Fix wrong messages in MC delete flow
5b82f92 opensm/osm_req.c: In osm_send_trap144, eliminate redundant clear of m_key in smp
9bf64dc opensm/osm_qos.c: Eliminate unneeded endport SL to VL setup
34b536c opensm/osm_sa_path_record.c: adding wrapper for pr_rcv_get_path_parms()
237b5d1 opensm/osm_mcast_mgr.c: Only route MLIDs with more than 1 member
a72db14 opensm/osm_trap_rcv.c: No need for heavy sweep when just NodeDescription changes
ea9a768 opensm/osmtest.c: fix bug in getting attr offset
a3dec3a iba/ib_types.h: remove assertion in ib_get_attr_offset()
6bc032a return no path when path does not exist
1592ae9 opensm: Better handling of non responsive SMAs
a69f01b opensm/osm_perfmgr.c: Remove unnecessary lock reference from Performance Manager object
167ade2 opensm: fixing compilation issues in some header files
e1c253e opensm/qos.c: Revert port ranges for calls to sl2vl_update_table().
0689f49 opensm/libvendor Reduce stack consumption
59056c7 opensm - address windows env issues
ff14200 opensm/osm_sa_multipath_record.c: livelock in mpr_rcv_get_path_parms
3f23d83 opensm/osm_sa_path_record.c: Add error code to newly added log message
7fc6cd3 ib_types.h add debug assert
4fd4ca3 osmtest - use helper function
6fdc20a opensm/complib use portable macro syntax
bf23d7c opensm: fixed getline pointer allocation free in osm_console_io
74867c7 Add node/port/qos information to some error messages
31a617d replace (long*)(long) casting with transportable data type (uintptr_t)
8da7521 opensm/st.c: fix potential core dumps
6a30911 opensm/osm_console.c: fix memory and file descriptor leaks
696f12c opensm/osm_qos_parser_y.y: fixing bunch of memory leaks on invalid values
3a7b97c opensm/osm_ucast_file.c: closing file descriptor in error path
b4575c5 opensm/osm_pkey_mgr.c: fixing small memory leak
dc0695f opensm/osm_ucast_lash.c: small bug in calculating allocated size
3c9604b opensm/osm_ucast_ftree.c: fixing another memory leak at error path
4460990 opensm/osm_ucast_ftree.c: fix small memory leak in error path
857cd6c opensm/osm_trap_rcv.c: fix possible core dump
b74bef5 opensm/osm_trap_rcv.c: No need to check for sweep for trap 145
81dade3 opensm/osm_ucast_ftree: When roots are not connected, update hop count but not lft
acf2337 osmtest/osmt_service.c: In osmt_run_service_records_flow, add missing status
6db7f4a opensm/osm_state_mgr.c: Don't signal DISCOVER to SM state machine when already DISCOVERING
28693c5 Fix autotools to include the necessary M4 files
c1c8730 osm_vl15intf.c: fixing use-after-free coredump
3353f9b opensm/osm_helper.c: use ARR_SIZE macro instead of hardcoded values
2da9849 osmtest/osmt_slvl_vl_arb.c: handling fopen() failure
f48d5ea opensm/osm_db_files.c: malloc() return value run-time check
ea3ef82 opensm/osm_db_files.c: fix small memory leak
f4a5174 opensm/osm_subnet.c: fixing small bug in error path
c18ef23 opensm/osm_mesh.c: fixing a bug in compare_switches()
83b74cd opensm/osm_helper.c: fix potential overrun of the array
85c0ac9 osmtest/osmtest.c: handle timeouts in PR stress test
ebb2c84 opensm/osm_node_info_rcv.c: move p_physp declaration under code block
5c88113 opensm/osm_node_info_rcv.c: remove useless code line
866d939 opensm/osm_sa_vlarb_record.c: removed unused variable
15a8770 opensm/osm_sa_pkey_record.c: removing unused variable
8f002b7 opensm/osm_pkey.c: removing unused function
9c0fa2f opensm/osm_sminfo_rcv.c: removing unused variable
d3f060a opensm/osm_mtree.c: removing useless 'if' statement
bbef64a libvendor/osm_vendor_mlx_sa.c: remove useless "if" statement
2da02b5 libvendor/osm_vendor_ibumad_sa.c: remove useless "if" statement
435dde0 opensm/sa: simplify osm_mcmr_rcv_find_or_create_new_mgrp() function call
e7a872d opensm/osm_qos_policy.c: change a log message
ffbe7d0 opensm: Cause status of unicast routing attempt to propogate to callers of osm_ucast_mgr_process().
673877a opensm: Make mcast_mgr_purge_tree() available outside osm_mcast_mgr.c.
b135687 opensm: Track the minimum value in the fabric of data VLs supported.
7c1ee64 opensm: Fix typo in routing section in man page and doc
d206011 opensm/osmtest/osmtest.c: inventory file parsing bugfix
e01121a fixed deprecated conversion from string constant to char* warning
a7ba101 opensm/main.c: Change size parameter in setvbuf call from 0 to BUFSIZ
2a92554 opensm/osmtest/osmt_multicast.c: Fix multicast flow failures on pkey validation
364e65b opensm/osm_dump.c: Fix FDR10 speed dumping
80e11b9 osmtest/osmt_multicast.c: Fix check of partial JoinState delete request - removing NonMember (o15.0.1.14)
78e1e4e osmtest/osmt_multicast.c: Fix check of BAD RATE when connecting to existing MGID (o15.0.1.13)
e94e972 osmtest/osmt_multicast.c: Fix first MGID=0 MC group creation case
daedad7 opensm/osmtest/osmt_multicast.c: Fix an unrealistic rate case
110ae10 opensm: fixed segfault in osm_destroy
d666205 opensm/osm_prtn.c: Fix typo in log message
f3ccb45 opensm: Remove duplicate definition of IB_MAD_STATUS_CLASS_MASK
917070c Move no_fallback_routing_engine from osm_subn_opt_t to osm_opensm_t.
d71a924 Free memory from osm_subn_opt_t when osm_subn_t destroyed
1b75fa4 Remove duplicate initialization of scatter_ports
b5f4570 Do not load configs from the default config file and specified config file
f24a089 Fix memleak and segfault
247d0d8 Fix IPoIB broadcast group creation on non-default Pkey
78d86bd opensm/osm_subnet.c: Trivial optimization to code flow in subn_verify_sl2vl
6bb41e3 opensm/complib/cl_atomic.h: Commentary changes
28ee7b9 opensm/osm_sa_class_port_info.c: Conditionalize setting of OSM_CAP2_IS_MCAST_TOP_SUPPORTED
29e59a2 Removed unused parameter "ib_mad_addr" from umad_reciever()
63ad0bb opensm/osmeventplugin/src/osmeventplugin.c: Output LIDs in decimal
17967c2 opensm/osm_switch.h: Fix commentary typo
6d3e223 opensm/osm_perfmgr.c: Enhance send error log message
08abcd4 opensm/osm_sa_mad_ctrl.c: Enhance send error log message
13f3e0f opensm/libvendor/osm_vendor_ibumad.c: Fix DR path printing on send timeouts
0dfd760 Fix suggest parentheses around operand warning
ad2dbf8 Support source-target-port-guid QoS policy configuration with ULP 'any'
080e3ad Support source-port-guid QoS policy configuration with ULP 'any'
49777a9 Fix typo in qos-ulps parsing comment
bcfe1b9 opensm/osm_torus.c: Use "OpenSM standard" error codes
683397d Fix use of GNU old-style field designator extension
719fcd4 Fix use of logical && with constant operand; switch to bitwise &
6bc87bd opensm: fix search common pkeys
a6ac5e3 opensm/osm_drop_mgr.c: GID out trap fix
7717505 opensm/osm_pkey_mgr.c: fix segfault when trying to access not allocated block
eb90efd include/opensm/osm_subnet.h: fix comment typos
0a315e3 opensm/include/iba/ib_types.h: fix comment typos and errors
125baa0 opensm/opensm.8.in: Fix cut 'n paste error
f0d14d2 opensm/osm_ucast_ftree.c: Fix some typos
841096f opensm: Fix opensm handover/relinquish corner case
bf420ac opensm/osm_helper.c: Fix commentary typo
2be999c opensm: fixed description in osm_routing_engine
cf3d185 gen_chlog.sh: fixed version ordering
8d49c5d opensm/osm_sa_guidinfo_record.c: Fix locking
8d764b8 Fix continous looping when clearing accum_pkeys table
32500a6 opensm/osm_link_mgr.c: Fix sending PortInfo Set for ports supporting extended speed
e24ff39 Fix Pkey enforcement configuration
041e47a Fix PathRecord reply to be the same for allow_both_pkeys ON and OFF
3c9b81d OpenSM/osm_prtn_config.c: Fix non-initialized pointer usage
28a40a2 Fix logging messages about op_vls and mtu mismatch
5e672a6 Fix memory leak on dfsssp_context_destroy()
3a87fa2 opensm/osm_req.c: fix first sweep m_key search algorithm
2108431 opensm: fix default cc_max_outstanding_mads assignment
dae214f opensm/osm_log.h: fix function documentation
1cf0bb9 opensm/osm_congestion_control.c: fix use-after-free found by coverity
ab5af49 opensm/osm_ucast_dfsssp.c : fix dereference before null check
b6a2f6a opensm/osm_ucast_dfsssp.c : fix dereference null return value
df66c1f opensm: Fix incorrect use of sizeof
c58a416 opensm/osm_dump.c: Fix output port on SL2VL table for non switch nodes
8af5bce opensm/osm_sa_slvl_record.c: Fix out port for CAs and routers
18a77f9 opensm/osm_torus.c: Fix memory leak
4ccbdc1 Fix -Wformat-security warnings with clang
c762389 Fix -Wtautological-compare warnings with clang
9e6dd8f Fix linker error with clang with -O < 2
27da5eb opensm/include/osm_opensm.h: Fix commentary typo
b9067ba opensm/include/complib/cl_packon.h: Fix some commentary typos
e522f74 opensm/osm_perfmgr_db.c: Fix output error due to possible 32bit int overflow
6f94c4c opensm: Fix signed vs unsigned int comparison
2ea2cf3 opensm/osm_vl15intf.c: Fix commentary typo
f396936 opensm/complib/cl_atomic_osd.h: Fix long standing bug in cl_atomic_sub
606157c osmtest/osmt_multicast.c: Fix 02BF error
03d55f6 opensm/osm_sw_info_rcv.c: Fixed locking issue on osm_get_node_by_guid error
d7e4da1 opensm: Use IB_PATH_SELECTOR_EXACTLY rather than harded coded constant
735c86d opensm/osm_sa_informinfo.c: Fix infr_rcv_respond to only copy InformInfo
074ec5a osm_perfmgr.h: Cosmetic formatting changes
05af776 osm_drop_mgr.c: Add missing assert
de254f9 osm_opensm.c: Add missing ERR number
b1e58bb osmeventplugin: Fix compile warning
48c9f43 osm_prtn_config.c: Some changes to osm_prtn_config_parse_file
6321afa osm_subnet.c: Cosmetic change to config file output
aaa7b1e osm_db_files.c: Some minor fixes/improvements to osm_db_store
3cae07c osm_perfmgr.c: Cosmetic formatting changes
07b24a3 osm_port_info_rcv.c: Reset client reregister bit only on a response to
SET
84c9832 osm_ucast_ftree.c: replace assert with error return value
1c0f0 osm_sa_path_record.c: Improve ERR 1F1D to show the pkey specified in
PathQuery
94e99a8 osm_mcast_mgr.c: Removed mcast_mgr_purge_tree_node due to code
duplication
929934e osm_pkey.[h c]: Remove dead function osm_pkey_tbl_clear_accum_pkeys
bf42ec8 osm_mtree.c: Cosmetic change in osm_mtree_destroy function
7e96f32 osm_sm_state_mgr.c: Fix handling of polling retry number
78d87b9 osm_mcast_mgr.c: fixed missing error message number
a1df1d7 osm_state_mgr.c: Fix error print in state_mgr_check_tbl_consistency()
1441937 osm_mcast_mgr.c Add block number to error message
edd5e74 Redundant remove() function call during db file generation
dcb2df0 osm_link_mgr.c: Fix uninitialized value (physp0)
7160cce osm_link_mgr.c: Fix uninitialized value (physp0)
5aa4ea3 osm_link_mgr.c: fix uninitialized variable usage
cf014ec reduce log level for missing partition configuration file.
d3aeae8 osmtest: Handle other than default subnet prefix
181c863 osm_vendor_ibumad.c: Improve ERR 5430 log message
7321689 osm_state_mgr.c Add info to some error messages
0fc753d osm_sa_path_record.c: Eliminate extraneous space in 1F1A log message
18b3be9 Use trap number defines rather than actual trap numbers
270a700 osm_db_files.c: Add osm_db_domain_init failure handling into test
program
2b82c1c Handle memory allocation failure in osm_db_domain_init()
8284132 Use after free in osm_prtn_delete
e186b4a osm_sa_guidinfo_record.c: False duplicate GUID error messages
d95d461 osm_sa_informinfo.c Add attribute info to log messages
94789a8 osm_port_info_rcv.c Issue a log message if we cannot read the MKey of
a port
b67db2b osm_helper.c: Fix out-of-bounds read
ef86015 osm_db_files.c : Fix resource leak guid2lid parser
4a2d2d8 osm_subnet.c: Fix memory leak caused by commit
dc0760cb8088fbe079e19682570a884ba01e94ff
168eaeb osm_db_files.c: Fix memory leak when deleting entries from osm db
edfaddc osm_ucast_dfsssp.c: Fix some typos
e1804f4 dfsssp - add missing and change existing return values
d5ef9af update man page and usage explanation for --lfts_file
6fdd844 osm_sa_path_record.c: Fix rate setting issue in SA PR handling
4d6925c osm_vendor_ibumad.c: Fix explicit null derefenced issue found by
coverity
2d67f3e opensm/osm_db_pack.c: Removed uneeded asserts
09e1e7e libvendor/osm_pkt_randomizer.c: Fix broken compilation with vendor sim
6ff99aa complib/cl_event_wheel.c: Add print of num_regs in cl_event_wheel_dump
a3957f2 complib/cl_event_wheel.c: Fix duplicate error codes
ceb4041 Fix test scenario in cl_event_wheel
5824714 osmeventplugin/osmeventplugin.c: Add Mellanox copyright
7186965 osm_event_plugin.h: Add Mellanox copyright
cd3b715 osmtest: Make the "-guid" option's argument mandatory
0154977 complib/cl_event_wheel.h: Some cosmetic fixes
b78b1d5 osm_sa_mcmember_record.c validate_requested_mgid returning boolean
58ee065 osm_ucast_mgr.c: Fix extra copy in set_lft_block routine
5bf6e72 osm_ucast_mgr.c: Fix duplicated code for fallback routing engine
2e5966e osm_sm_state_mgr.c Trivial log changes
aed1675 Log changes related to event subscription and forwarding
75bba51 Minor log formatting changes
16b4dfc Some log changes
51f87ee Add attribute information to SA request error messages
abd47cc osm_sa_mcmember_record.c Reduce number of error messages the for same
event
ae9d7e7 Add trap details to notice log message
886de5d ib_types.h: Fix commentary typo
234401b cl_threadpool.h: Remove vestigial mention of cl_thread_pool_construct
e20f37a osm_subnet.c Remove empty syslog message
898e9a3 osm_ucast_lash.c: Cosmetic formatting change
6d0413d opensm/osm_ucast_dfsssp.c: Fix unused variable in update_mcft()
583d4cf osm_console_io.c: Handle another write-strings issue
cbbe385 osm_sa_mcmember_record.c: Improve debug log message in validate_modify
4f835ae osm_congestion_control.c: Simplify some code
23ebbe9 osm_log.c: Remove unneeded initialization in osm_log
550fdeb osm_lid_mgr.c: Some commentary fixes/updates
1d50845 opensm: Add configure output messages for several configure options
91384ed osm_lid_mgr.c: Fix a couple of commentary typos
a238800 ib_types.h: Trap 144 PortInfo:CapabilityMask2 changed bit definition
564b6eb opensm: fix dfsssp uninitialized value
395157e opensm/osm_node.h: Fix some commentary typos
664494a opensm: Fix issues causing const warnings for strings
8840f82 opensm/osm_switch.h: Cosmetic change
1a06167 opensm/man/osmtest.8: Add option for using full world path queries
eb43f7e opensm/osm_torus.c: clarify log messages on stale priv pointers
1f7ceb8 opensm/osmtest: fix debug build
dc0760c opensm/osm_subnet.c: Miscellaneous minor fixes
4924ea7 osmtest: Add support for full world path records back as option
6f1a67e osmtest/osmtest.c: Minor reordering of code in
osmtest_write_all_path_recs
e9556df opensm/osmtest.c: half_world_query when creating inventory file
81d3ea3 opensm/osm_subnet.c: Change default for perfmgr_query_cpi to FALSE
309317d opensm/osm_ucast_ftree.c: Fix invalid debug output message
d0a8532 opensm/perfmgr: clean up: break out redirect processing from
pc_recv_process
ef32e12 opensm: make osm_pr_rcv_get_end_points, osm_pr_rcv_process_pair,
osm_pr_rcv_process_half public
7723c07 opensm: make osm_get_path_params public
7ac18bb opensm/osm_perfmgr.c: Cosmetic changes
fe52571 opensm/perfmgr: add failed port guid to error message
8b67a1c opensm/osm_req.c: Better implementation of req_determine_mkey
37eecc8 opensm/libibvendor: osm_vendor_get_all_port_attr include sm_sl value
in port attribute struct
8ac930e osmtest/osmt_multicast.c: Fix IPoIB MC group recognition
5a33cc6 osmtest/osmt_multicast.c: Fix commentary typo
186a598 opensm/osm_sa_multipath_record.c: Fix commentary typo
4cb3751 osm_dump.c: Fix typo in dump_lid_matrix
a0deac6 opensm/osm_dump.c: Fix enhanced switch port 0 handling in
print_node_report
abbbe8a opensm/ib_types.h: Add missing IB_MPR_COMPMASK_SERVICEID define
505d48b opensm/osm_torus.c: Cosmetic formatting changes
dc44d48 opensm/ib_types.h: Add missing IB_PR_COMPMASK_SERVICEID define

* Other less critical or visible bugs were also fixed.

5 Main Verification Flows
-------------------------

OpenSM verification is run using the following activities:
* osmtest - a stand-alone program
* ibmgtsim (IB management simulator) based - a set of flows that
  simulate clusters, inject errors and verify OpenSM capability to
  respond and bring up the network correctly.
* small cluster regression testing - where the SM is used on back to
  back or single switch configurations. The regression includes
  multiple OpenSM dedicated tests.
* cluster testing - when we run OpenSM to setup a large cluster, perform
  hand-off, reboots and reconnects, verify routing correctness and SA
  responsiveness at the ULP level (IPoIB and SDP).

5.1 osmtest

osmtest is an automated verification tool used for OpenSM
testing. Its verification flows are described by list below.

* Inventory File: Obtain and verify all port info, node info, link and path
  records parameters.

* Service Record:
   - Register new service
   - Register another service (with a lease period)
   - Register another service (with service p_key set to zero)
   - Get all services by name
   - Delete the first service
   - Delete the third service
   - Added bad flows of get/delete  non valid service
   - Add / Get same service with different data
   - Add / Get / Delete by different component  mask values (services
     by Name & Key / Name & Data / Name & Id / Id only )

* Multicast Member Record:
   - Query of existing Groups (IPoIB)
   - BAD Join with insufficient comp mask (o15.0.1.3)
   - Create given MGID=0 (o15.0.1.4)
   - Create given MGID=0xFF12A01C,FE800000,********,12345678 (o15.0.1.4)
   - Create BAD MGID=0xFA. (o15.0.1.6)
   - Create BAD MGID=0xFF12A01B w/ link-local not set (o15.0.1.6)
   - New MGID with invalid join state (o15.0.1.9)
   - Retry of existing MGID - See JoinState update (o15.0.1.11)
   - BAD RATE when connecting to existing MGID (o15.0.1.13)
   - Partial JoinState delete request - removing FullMember (o15.0.1.14)
   - Full Delete of a group (o15.0.1.14)
   - Verify Delete by trying to Join deleted group (o15.0.1.14)
   - BAD Delete of IPoIB membership (no prev join) (o15.0.1.15)

* GUIDInfo Record:
   - All GUIDInfoRecords in subnet are obtained

* MultiPathRecord:
   - Perform some compliant and noncompliant MultiPathRecord requests
   - Validation is via status in responses and IB analyzer

* PKeyTableRecord:
  - Perform some compliant and noncompliant PKeyTableRecord queries
  - Validation is via status in responses and IB analyzer

* LinearForwardingTableRecord:
  - Perform some compliant and noncompliant LinearForwardingTableRecord queries
  - Validation is via status in responses and IB analyzer

* Event Forwarding: Register for trap forwarding using reports
   - Send a trap and wait for report
   - Unregister non-existing

* Trap 64/65 Flow: Register to Trap 64-65, create traps (by
  disconnecting/connecting ports) and wait for report, then unregister.

* Stress Test: send PortInfoRecord queries, both single and RMPP and
  check for the rate of responses as well as their validity.


5.2 IB Management Simulator OpenSM Test Flows:

The simulator provides ability to simulate the SM handling of virtual
topologies that are not limited to actual lab equipment availability.
OpenSM was simulated to bring up clusters of up to 10,000 nodes. Daily
regressions use smaller (16 and 128 nodes clusters).

The following test flows are run on the IB management simulator:

* Stability:
  Up to 12 links from the fabric are randomly selected to drop packets
  at drop rates up to 90%. The SM is required to succeed in bringing the
  fabric up. The resulting routing is verified to be correct as well.

* LID Manager:
  Using LMC = 2 the fabric is initialized with LIDs. Faults such as
  zero LID, Duplicated LID, non-aligned (to LMC) LIDs are
  randomly assigned to various nodes and other errors are randomly
  output to the guid2lid cache file. The SM sweep is run 5 times and
  after each iteration a complete verification is made to ensure that all
  LIDs that could possibly be maintained are kept, as well as that all nodes
  were assigned a legal LID range.

* Multicast Routing:
  Nodes randomly join the 0xc000 group and eventually the
  resulting routing is verified for completeness and adherence to
  Up/Down routing rules.

* osmtest:
  The complete osmtest flow as described in the previous table is run on
  the simulated fabrics.

* Stress Test:
  This flow merges fabric, LID and stability issues with continuous
  PathRecord, ServiceRecord and Multicast Join/Leave activity to
  stress the SM/SA during continuous sweeps. InformInfo Set/Delete/Get
  were added to the test such both existing and non existing nodes
  perform them in random order.

5.3 OpenSM Regression

Using a back-to-back or single switch connection, the following set of
tests is run nightly on the stacks described in table 2. The included
tests are:

* Stress Testing: Flood the SA with queries from multiple channel
  adapters to check the robustness of the entire stack up to the SA.

* Dynamic Changes: Dynamic Topology changes, through randomly
  dropping SMP packets, used to test OpenSM adaptation to an unstable
  network & verify DB correctness.

* Trap Injection: This flow injects traps to the SM and verifies that it
  handles them gracefully.

* SA Query Test: This test exhaustively checks the SA responses to all
  possible single component mask. To do that the test examines the
  entire set of records the SA can provide, classifies them by their
  field values and then selects every field (using component mask and a
  value) and verifies that the response matches the expected set of records.
  A random selection using multiple component mask bits is also performed.

5.4 Cluster testing:

Cluster testing is usually run before a distribution release. It
involves real hardware setups of 16 to 32 nodes (or more if a beta site
is available). Each test is validated by running all-to-all ping through the IB
interface. The test procedure includes:

* Cluster bringup

* Hand-off between 2 or 3 SM's while performing:
  - Node reboots
  - Switch power cycles (disconnecting the SM's)

* Unresponsive port detection and recovery

* osmtest from multiple nodes

* Trap injection and recovery


6 Qualified Software Stacks and Devices
---------------------------------------

OpenSM Compatibility
--------------------
Note that OpenSM version 3.2.1 and earlier used a value of 1 in host
byte order for the default SM_Key, so there is a compatibility issue
with these earlier versions of OpenSM when the 3.2.2 or later version
is running on a little endian machine. This affects SM handover as well
as SA queries (saquery tool in infiniband-diags).


Table 2 - Qualified IB Stacks
=============================

Stack                                    | Version
-----------------------------------------|--------------------------
The main stream Linux kernel             |   2.6.x
OFED                                     |   1.5,1.5.x
OFED                                     |   1.4
OFED                                     |   1.3
OFED                                     |   1.2
OFED                                     |   1.1
OFED                                     |   1.0

Table 3 - Qualified Devices and Corresponding Firmware
======================================================

Mellanox
Device                              |   FW versions
------------------------------------|-------------------------------
InfiniScale                         | fw-43132  5.2.000 (and later)
InfiniScale III                     | fw-47396  0.5.000 (and later)
InfiniScale IV                      | fw-48436  7.1.000 (and later)
InfiniHost                          | fw-23108  3.5.000 (and later)
InfiniHost III Lx                   | fw-25204  1.2.000 (and later)
InfiniHost III Ex (InfiniHost Mode) | fw-25208  4.8.200 (and later)
InfiniHost III Ex (MemFree Mode)    | fw-25218  5.3.000 (and later)
ConnectX IB                         | fw-25408  2.3.000 (and later)

QLogic/PathScale
Device  |   Note
--------|-----------------------------------------------------------
iPath   | QHT6040 (PathScale InfiniPath HT-460)
iPath   | QHT6140 (PathScale InfiniPath HT-465)
iPath   | QLE6140 (PathScale InfiniPath PE-880)
iPath   | QLE7240
iPath   | QLE7280

Note 1: OpenSM does not run on an IBM Galaxy (eHCA) as it does not expose
QP0 and QP1. However, it does support it as a device on the subnet.

Note 2: QoS firmware and Mellanox devices

HCAs: QoS supported by ConnectX. QoS-enabled FW release is 2_5_000 and
later.

Switches: QoS supported by InfiniScale III
Any InfiniScale III FW that is supported by OpenSM supports QoS.
