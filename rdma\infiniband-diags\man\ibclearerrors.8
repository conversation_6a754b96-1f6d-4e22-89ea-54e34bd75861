.TH IBCLEARERRORS 8 "May 21, 2007" "OpenIB" "OpenIB Diagnostics"

.SH NAME
ibclearerrors \- clear error counters in IB subnet

.SH SYNOPSIS
.B ibclearerrors
[\-h] [\-N | \-nocolor] [<topology-file> | \-C ca_name \-P ca_port
\-t(imeout) timeout_ms]

.SH DESCRIPTION
.PP
ibclearerrors is a script which clears the PMA error counters in PortCounters
by either walking the IB subnet topology or using an already saved topology
file.

.SH OPTIONS
.PP
\-N | \-nocolor  use mono rather than color mode
.PP
\-C <ca_name>    use the specified ca_name.
.PP
\-P <ca_port>    use the specified ca_port.
.PP
\-t <timeout_ms> override the default timeout for the solicited mads.

.SH SEE ALSO
.BR ibnetdiscover(8),
.BR perfquery(8)

.SH AUTHOR
.TP
Hal Rosenstock
.RI < <EMAIL> >
