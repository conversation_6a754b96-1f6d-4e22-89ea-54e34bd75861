#!/bin/sh

IBPATH=${IBPATH:-@IBSCRIPTPATH@}

usage() {
	echo Usage: `basename $0` "[-h] [-b] [-v] [-N | -nocolor]" \
	    "[<topology-file> \| -C ca_name -P ca_port -t(imeout) timeout_ms]"
	exit -1
}

user_abort() {
	echo "Aborted"
	exit 1
}

trap user_abort SIGINT

gflags=""
verbose=""
brief=""
v=0
ntype=""
nodeguid=""
topofile=""
ca_info=""

while [ "$1" ]; do
	case $1 in
	-h)
		usage
		;;
	-N|-nocolor)
		gflags=-N
		;;
	-v)
		verbose=-v
		brief=""
		v=1
		;;
	-b)
		brief=-b
		verbose=""
		;;
	-P | -C | -t | -timeout)
		case $2 in
		-*)
			usage
			;;
		esac
		if [ x$2 = x ] ; then
			usage
		fi
		ca_info="$ca_info $1 $2"
		shift
		;;
	-*)
		usage
		;;
	*)
		if [ "$topofile" ]; then
			usage
		fi
		topofile="$1"
		;;
	esac
	shift
done

if [ "$topofile" ]; then
	netcmd="cat $topofile"
else
	netcmd="$IBPATH/ibnetdiscover $ca_info"
fi

text="`eval $netcmd`"
rv=$?
echo "$text" | awk '
BEGIN {
	ne=0
}
function check_node(lid, port)
{
	if (system("'$IBPATH'/ibchecknode -S '"$ca_info"' '$gflags' '$verbose' " lid)) {
		ne++
		print "\n# " ntype ": nodeguid 0x" nodeguid " failed"
		return 1;
	}
	return system("'$IBPATH'/ibdatacounts -S '"$ca_info"' '$gflags' '$verbose' '$brief' " lid " " port);
}

/^Ca/ || /^Switch/ || /^Rt/ {
			nnodes++
			ntype=$1; nodeguid=substr($3, 4, 16); ports=$2
			if ('$v')
				print "\n# Checking " ntype ": nodeguid 0x" nodeguid

			err = 0;
			if (ntype != "Switch")
				next

			lid = substr($0, index($0, "port 0 lid ") + 11)
			lid = substr(lid, 1, index(lid, " ") - 1)
			err = check_node(lid, 255)
		}
/^\[/	{
		nports++
		port = $1
		sub("\\(.*\\)", "", port)
		gsub("[\\[\\]]", "", port)
		if (ntype != "Switch") {
			lid = substr($0, index($0, " lid ") + 5)
			lid = substr(lid, 1, index(lid, " ") - 1)
			check_node(lid, port)
		} else if (err)
			system("'$IBPATH'/ibdatacounts -S '"$ca_info"' '$gflags' '$verbose' '$brief' " lid " " port);
}

/^ib/	{print $0; next}
/ibpanic:/	{print $0}
/ibwarn:/	{print $0}
/iberror:/	{print $0}

END {
   printf "*** WARNING ***: this command is deprecated; Please use \"ibqueryerrors --counters\n"
	printf "\n## Summary: %d nodes checked, %d bad nodes found\n", nnodes, ne
	printf "##          %d ports checked\n", nports
	exit (ne )
}
'

exit $rv
