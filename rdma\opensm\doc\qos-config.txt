Trivial low level QoS configuration proposition
===============================================

Basically there is a set of QoS related low-level configuration parameters.
All these parameter names are prefixed by "qos_" string. Here is a full
list of these parameters:

  qos_max_vls    - The maximum number of VLs that will be on the subnet
  qos_high_limit - The limit of High Priority component of VL Arbitration
                   table (IBA 7.6.9)
  qos_vlarb_low  - Low priority VL Arbitration table (IBA 7.6.9) template
  qos_vlarb_high - High priority VL Arbitration table (IBA 7.6.9) template
                   Both VL arbitration templates are pairs of VL and weight
  qos_sl2vl      - SL2VL Mapping table (IBA 7.6.6) template. It is a list
                   of VLs corresponding to SLs 0-15 (Note the VL15 used
                   here means drop this SL)

Typical default values (hard-coded in OpenSM initialization) are:

  qos_max_vls 15
  qos_high_limit 0
  qos_vlarb_low 0:0,1:4,2:4,3:4,4:4,5:4,6:4,7:4,8:4,9:4,10:4,11:4,12:4,13:4,14:4
  qos_vlarb_high 0:4,1:0,2:0,3:0,4:0,5:0,6:0,7:0,8:0,9:0,10:0,11:0,12:0,13:0,14:0
  qos_sl2vl 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,7

The syntax is compatible with rest of OpenSM configuration options and
values may be stored in OpenSM config file (cached options file).

In addition to the above, we may define separate QoS configuration
parameters sets for various target types. As targets, we currently support
CAs, routers, switch external ports, and switch's enhanced port 0. The
names of such specialized parameters are prefixed by "qos_<type>_"
string. Here is a full list of the currently supported sets:

  qos_ca_  - QoS configuration parameters set for CAs.
  qos_rtr_ - parameters set for routers.
  qos_sw0_ - parameters set for switches' port 0.
  qos_swe_ - parameters set for switches' external ports.

Examples:

  qos_sw0_max_vls 2
  qos_ca_sl2vl 0,1,2,3,5,5,5,12,12,0,
  qos_swe_high_limit 0
