.\" Man page generated from reStructuredText.
.
.TH IBSTAT 8 "@BUILD_DATE@" "" "Open IB Diagnostics"
.SH NAME
ibstat \- query basic status of InfiniBand device(s)
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
ibstat [options] <ca_name> [portnum]
.SH DESCRIPTION
.sp
ibstat is a binary which displays basic information obtained from the local
IB driver. Output includes LID, SMLID, port state, link width active, and port
physical state.
.sp
It is similar to the ibstatus utility but implemented as a binary rather
than a script. It has options to list CAs and/or ports and displays more
information than ibstatus.
.SH OPTIONS
.INDENT 0.0
.TP
.B \fB\-l, \-\-list_of_cas\fP
list all IB devices
.TP
.B \fB\-s, \-\-short\fP
short output
.TP
.B \fB\-p, \-\-port_list\fP
show port list
.TP
.B \fBca_name\fP
InfiniBand device name
.TP
.B \fBportnum\fP
port number of InfiniBand device
.UNINDENT
.SS Debugging flags
.\" Define the common option -d
.
.INDENT 0.0
.TP
.B \-d
raise the IB debugging level.
May be used several times (\-ddd or \-d \-d \-d).
.UNINDENT
.\" Define the common option -h
.
.sp
\fB\-h, \-\-help\fP      show the usage message
.\" Define the common option -v
.
.INDENT 0.0
.TP
.B \fB\-v, \-\-verbose\fP
increase the application verbosity level.
May be used several times (\-vv or \-v \-v \-v)
.UNINDENT
.\" Define the common option -V
.
.sp
\fB\-V, \-\-version\fP     show the version info.
.SS Configuration flags
.\" Define the common option -z
.
.sp
\fB\-\-config, \-z  <config_file>\fP Specify alternate config file.
.INDENT 0.0
.INDENT 3.5
Default: @IBDIAG_CONFIG_PATH@/ibdiag.conf
.UNINDENT
.UNINDENT
.SH EXAMPLES
.INDENT 0.0
.TP
.B ::
ibstat            # display status of all ports on all IB devices
ibstat \-l         # list all IB devices
ibstat \-p         # show port guids
ibstat mthca0 2   # show status of port 2 of \(aqmthca0\(aq
.UNINDENT
.SH SEE ALSO
.sp
ibstatus (8)
.SH AUTHOR
.INDENT 0.0
.TP
.B Hal Rosenstock
< \fI\%<EMAIL>\fP >
.UNINDENT
.\" Generated by docutils manpage writer.
.
