.TH IBCHECKWIDTH 8 "May 21, 2007" "OpenIB" "OpenIB Diagnostics"

.SH NAME
ibcheckwidth \- find 1x links in IB subnet

.SH SYNOPSIS
.B ibcheckwidth
[\-h] [\-v] [\-N | \-nocolor] [<topology-file> | \-C ca_name
\-P ca_port \-t(imeout) timeout_ms]


.SH DESCRIPTION
.PP
ibcheckwidth is a script which uses a full topology file that was created by
ibnet<PERSON><PERSON>, scans the network to validate the active link widths and
reports any 1x links.

.SH OPTIONS
.PP
\-N | \-nocolor  use mono rather than color mode
.PP
\-C <ca_name>    use the specified ca_name.
.PP
\-P <ca_port>    use the specified ca_port.
.PP
\-t <timeout_ms> override the default timeout for the solicited mads.

.SH SEE ALSO
.BR ibnetdiscover(8),
.BR ibchecknode(8),
.BR ibcheckportwidth(8)

.SH AUTHOR
.TP
Hal Rosenstock
.RI < <EMAIL> >
