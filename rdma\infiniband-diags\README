InfiniBand Diagnostic Tools

infiniband-diags is a set of utilities designed to help configure, debug, and
maintain infiniband fabrics.  Many tools and utilities are provided.  Some with
similar functionality.

In addition to the utilities provided a sub-library libibnetdisc is provided to
scan an entire IB fabric and return data structures representing it.  The
interface to this library is _not_ guaranteed to be stable (though we try.)

Dependencies:

	1) libibumad >= 1.3.7
	2) opensm-libs >= 3.3.10
	3) ib_umad kernel module

Python dependencies:
	1) docutils

Release notes 2.0.0 => 2.1.0

   1) New device IDs
   2) Add file of ports pairs in ibtracert
   3) ibqueryerrors additional counters
   4) HDR support
   5) Bug Fixes


Authors since 2.1.0

267969c19dca  :  <PERSON>  :  ibdiags: Avoid using the same variable as big and little endian
36742a3bccb9  :  <PERSON>  :  ibdiags: Fix incorrect forming of comp_mask
eddc3e7d309e  :  <PERSON>  :  ibdiags: Fix extra swap on error print
c993deca2ba1  :  <PERSON>  :  ibdiags: Fix missing swaps on remap_node_name
16168163317b  :  <PERSON>  :  ibdiags: Use cl_qmap instead of glib hashtable
fef813c05611  :  <PERSON>le Verrier  :  RPM spec: Download source from github.com/linux-rdma/infiniband-diags
66a7e925a15f  :  Jason Gunthorpe  :  ibdiags: Add __be* annotations to types
42d22821ece2  :  Jason Gunthorpe  :  ibdiags: Add missing ULL on large constants
c6eb39614d32  :  Jason Gunthorpe  :  ibdiags: Add missing endian conversions on constants
ba9e4e0d83ad  :  Jason Gunthorpe  :  ibdiags: Removing unneeded casting around endian conversions
abe3488b3ddb  :  Jason Gunthorpe  :  ibdiags: Fix reversed endian swap direction
c5489ce9fe07  :  Jason Gunthorpe  :  ibdiags: Use NULL instead of 0 in all places where it used as a pointer
511eaed058ce  :  Jason Gunthorpe  :  ibdiags: Use PRIx64 when printing uint64_t
c9d98a6ac045  :  Jason Gunthorpe  :  ibdiags: Add missing static to variables
7735a8795120  :  Jason Gunthorpe  :  ibdiags: Remove always true test
35fba200e833  :  Jason Gunthorpe  :  ibdiags: Mark functions taking format strings with attribute format
552e22438e95  :  Jason Gunthorpe  :  ibdiags: Do not use optarg for process_opt
24ebfd5857a6  :  Jason Gunthorpe  :  ibdiags: Remove IN/OUT notations
f973fda0a755  :  Jason Gunthorpe  :  ibdiags: Replace ib_netXX_t with __beXX
176d9de7605c  :  Jason Gunthorpe  :  ibdiags/ibping: Use clock_gettime instead of complib
b7fb37e2fa0e  :  Ira Weiny  :  ibdiags: Replace cl_hton with htobe
b8b5393762b8  :  Jason Gunthorpe  :  ibdiags: Replace cl_hton with htobe
c995abe807cd  :  Jason Gunthorpe  :  ibdiags: Replace cl_ntoh with betoh
45ba74fd74a6  :  Mark Haywood  :  ibdiags: Fix buffer overflow in sa_report_err()
90f328abba56  :  Mark Haywood  :  ibdiags: Fix buffer overflow in sa_report_err()
1398c31cb5df  :  Ira Weiny  :  infiniband-diags: Remove snprintf warnings
11edb997a23a  :  Jason Gunthorpe  :  ibdiags/ibtracert: Remove srcport parameter
78ce6b01a7f8  :  Jason Gunthorpe  :  ibdiags/iblinkinfo: Move guid globals to a stuct
09f10ecbfbdc  :  Jason Gunthorpe  :  ibdiags/perfquery: Move many of the globals into a struct
cd1b0d6da91e  :  Jason Gunthorpe  :  ibdiags: Rename local variables to avoid shadow
779b5d76a068  :  Jason Gunthorpe  :  ibdiags: Remove parameters that are always passed as their global values
187b3faa318d  :  Jason Gunthorpe  :  ibdiags: Remove unneeded prototype
ebc33addce8a  :  Jason Gunthorpe  :  ibdiags: Use a static inline for the empty debug
e98bef85a90f  :  Jason Gunthorpe  :  ibdiag: Use modern C function declarations
09922b359ce6  :  Jason Gunthorpe  :  ibdiags: Make functions used in only one file static
dd5365dbb2e6  :  Jason Gunthorpe  :  ibdiags: Put the prototype for mlnx_ext_port_infor_err in a header
b90d9b39c207  :  Jason Gunthorpe  :  libibmad: Use {} instead of {0}
54b6fe378289  :  Jason Gunthorpe  :  ibdiags: Use const char when appropriate
4517747c2ad9  :  Jason Gunthorpe  :  ibdiags: Remove unused functions
************  :  Jason Gunthorpe  :  libibmad: Remove madrpc_def_timeout
5e5657c05380  :  Hal Rosenstock  :  Add latest Bull device IDs to device white lists
eda517d6b731  :  Cyrille Verrier  :  Update README: list python dependencies (docutils)
2138894e214c  :  Cyrille Verrier  :  Update .gitignore
48baa814d842  :  Hal Rosenstock  :  Add latest new device ID to device white lists

