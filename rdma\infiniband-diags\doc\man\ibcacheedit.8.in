.\" Man page generated from reStructuredText.
.
.TH IBCACHEEDIT 8 "@BUILD_DATE@" "" "Open IB Diagnostics"
.SH NAME
ibcacheedit \- edit an ibnetdiscover cache
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
ibcacheedit [options] <orig.cache> <new.cache>
.SH DESCRIPTION
.sp
ibcacheedit allows users to edit an ibnetdiscover cache created through the
\fB\-\-cache\fP option in \fBibnetdiscover(8)\fP .
.SH OPTIONS
.INDENT 0.0
.TP
.B \fB\-\-switchguid BEFOREGUID:AFTERGUID\fP
Specify a switchguid that should be changed.  The before and after guid
should be separated by a colon.  On switches, port guids are identical
to the switch guid, so port guids will be adjusted as well on switches.
.TP
.B \fB\-\-caguid BEFOREGUID:AFTERGUID\fP
Specify a caguid that should be changed.  The before and after guid
should be separated by a colon.
.TP
.B \fB\-\-sysimgguid BEFOREGUID:AFTERGUID\fP
Specify a sysimgguid that should be changed.  The before and after guid
should be spearated by a colon.
.TP
.B \fB\-\-portguid NODEGUID:BEFOREGUID:AFTERGUID\fP
Specify a portguid that should be changed.  The nodeguid of the port
(e.g. switchguid or caguid) should be specified first, followed by a
colon, the before port guid, another colon, then the after port guid.
On switches, port guids are identical to the switch guid, so the switch
guid will be adjusted as well on switches.
.UNINDENT
.SS Debugging flags
.\" Define the common option -h
.
.sp
\fB\-h, \-\-help\fP      show the usage message
.\" Define the common option -V
.
.sp
\fB\-V, \-\-version\fP     show the version info.
.SH AUTHORS
.INDENT 0.0
.TP
.B Albert Chu
< \fI\%<EMAIL>\fP >
.UNINDENT
.\" Generated by docutils manpage writer.
.
