.TH IBCLEARCOUNTERS 8 "May 21, 2007" "OpenIB" "OpenIB Diagnostics"

.SH NAME
ibclearcounters \- clear port counters in IB subnet

.SH SYNOPSIS
.B ibclearcounters
[\-h] [<topology-file> | \-C ca_name \-P ca_port \-t(imeout) timeout_ms]

.SH DESCRIPTION
.PP
ibclearcounters is a script that clears the PMA port counters by either walking
the IB subnet topology or using an already saved topology file.

.SH OPTIONS
.PP
\-C <ca_name>    use the specified ca_name.
.PP
\-P <ca_port>    use the specified ca_port.
.PP
\-t <timeout_ms> override the default timeout for the solicited mads.

.SH SEE ALSO
.BR ibnetdiscover(8),
.<PERSON> perfquery(8)

.SH AUTHOR
.TP
Hal Rosenstock
.RI < <EMAIL> >
