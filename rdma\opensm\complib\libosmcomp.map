OSMCOMP_2.3 {
	global:
		complib_init;
		complib_exit;
		cl_is_debug;
		cl_disp_construct;
		cl_disp_init;
		cl_disp_destroy;
		cl_disp_register;
		cl_disp_unregister;
		cl_disp_post;
		cl_disp_shutdown;
		cl_disp_get_queue_status;
		cl_event_construct;
		cl_event_init;
		cl_event_destroy;
		cl_event_signal;
		cl_event_reset;
		cl_event_wait_on;
		cl_event_wheel_construct;
		cl_event_wheel_init;
		cl_event_wheel_init_ex;
		cl_event_wheel_destroy;
		cl_event_wheel_dump;
		cl_event_wheel_reg;
		cl_event_wheel_unreg;
		cl_event_wheel_num_regs;
		cl_qlist_insert_array_head;
		cl_qlist_insert_array_tail;
		cl_qlist_insert_list_head;
		cl_qlist_insert_list_tail;
		cl_is_item_in_qlist;
		cl_qlist_find_next;
		cl_qlist_find_prev;
		cl_qlist_apply_func;
		cl_qlist_move_items;
		cl_list_construct;
		cl_list_init;
		cl_list_destroy;
		cl_list_remove_object;
		cl_is_object_in_list;
		cl_list_insert_array_head;
		cl_list_insert_array_tail;
		cl_list_find_from_head;
		cl_list_find_from_tail;
		cl_list_apply_func;
		cl_log_event;
		cl_qmap_init;
		cl_qmap_get;
		cl_qmap_get_next;
		cl_qmap_apply_func;
		cl_qmap_insert;
		cl_qmap_remove_item;
		cl_qmap_remove;
		cl_qmap_merge;
		cl_qmap_delta;
		cl_map_construct;
		cl_map_init;
		cl_map_destroy;
		cl_map_insert;
		cl_map_get;
		cl_map_get_next;
		cl_map_remove_item;
		cl_map_remove;
		cl_map_remove_all;
		cl_map_merge;
		cl_map_delta;
		cl_fmap_init;
		cl_fmap_match;
		cl_fmap_get;
		cl_fmap_get_next;
		cl_fmap_apply_func;
		cl_fmap_insert;
		cl_fmap_remove_item;
		cl_fmap_remove;
		cl_fmap_merge;
		cl_fmap_delta;
		cl_qcpool_construct;
		cl_qcpool_init;
		cl_qcpool_destroy;
		cl_qcpool_grow;
		cl_qcpool_get;
		cl_qcpool_get_tail;
		cl_qpool_construct;
		cl_qpool_init;
		cl_cpool_construct;
		cl_cpool_init;
		cl_pool_construct;
		cl_pool_init;
		cl_ptr_vector_construct;
		cl_ptr_vector_init;
		cl_ptr_vector_destroy;
		cl_ptr_vector_at;
		cl_ptr_vector_set;
		cl_ptr_vector_remove;
		cl_ptr_vector_set_capacity;
		cl_ptr_vector_set_size;
		cl_ptr_vector_set_min_size;
		cl_ptr_vector_apply_func;
		cl_ptr_vector_find_from_start;
		cl_ptr_vector_find_from_end;
		cl_spinlock_construct;
		cl_spinlock_init;
		cl_spinlock_destroy;
		cl_spinlock_acquire;
		cl_spinlock_release;
		cl_status_text;
		cl_thread_construct;
		cl_thread_init;
		cl_thread_destroy;
		cl_thread_suspend;
		cl_thread_stall;
		cl_proc_count;
		cl_is_current_thread;
		cl_thread_pool_construct;
		cl_thread_pool_init;
		cl_thread_pool_destroy;
		cl_thread_pool_signal;
		__cl_timer_prov_create;
		__cl_timer_prov_destroy;
		cl_timer_construct;
		cl_timer_init;
		cl_timer_destroy;
		cl_timer_start;
		cl_timer_stop;
		cl_timer_trim;
		cl_get_time_stamp;
		cl_get_time_stamp_sec;
		cl_vector_construct;
		cl_vector_init;
		cl_vector_destroy;
		cl_vector_at;
		cl_vector_set;
		cl_vector_set_capacity;
		cl_vector_set_size;
		cl_vector_set_min_size;
		cl_vector_apply_func;
		cl_vector_find_from_start;
		cl_vector_find_from_end;
		cl_heap_construct;
		cl_heap_init;
		cl_heap_destroy;
		cl_heap_modify_key;
		cl_heap_insert;
		cl_heap_delete;
		cl_heap_extract_root;
		cl_heap_resize;
		cl_verify_heap_property;
		cl_is_stored_in_heap;
		cl_atomic_spinlock;
		cl_atomic_dec;
		ib_error_str;
		ib_async_event_str;
		ib_wc_status_str;
		open_node_name_map;
		close_node_name_map;
		parse_node_map;
		remap_node_name;
		clean_nodedesc;
		complib_init_v2;
	local: *;
};
