.\" Man page generated from reStructuredText.
.
.TH IBSTATUS 8 "@BUILD_DATE@" "" "Open IB Diagnostics"
.SH NAME
ibstatus \- query basic status of InfiniBand device(s)
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
ibstatus [\-h] [devname[:port]]...
.SH DESCRIPTION
.sp
ibstatus is a script which displays basic information obtained from the local
IB driver. Output includes LID, SMLID, port state, link width active, and port
physical state.
.SH OPTIONS
.\" Define the common option -h
.
.sp
\fB\-h, \-\-help\fP      show the usage message
.INDENT 0.0
.TP
.B \fBdevname\fP
InfiniBand device name
.TP
.B \fBportnum\fP
port number of InfiniBand device
.UNINDENT
.SH EXAMPLES
.INDENT 0.0
.TP
.B ::
ibstatus                    # display status of all IB ports
ibstatus mthca1             # status of mthca1 ports
ibstatus mthca1:1 mthca0:2  # show status of specified ports
.UNINDENT
.SH SEE ALSO
.sp
\fBibstat (8)\fP
.SH AUTHOR
.INDENT 0.0
.TP
.B Hal Rosenstock
< \fI\%<EMAIL>\fP >
.UNINDENT
.\" Generated by docutils manpage writer.
.
