IBMAD_1.3 {
	global:
		xdump;
		mad_dump_field;
		mad_dump_val;
		mad_print_field;
		mad_dump_array;
		mad_dump_bitfield;
		mad_dump_hex;
		mad_dump_int;
		mad_dump_linkdowndefstate;
		mad_dump_linkspeed;
		mad_dump_linkspeeden;
		mad_dump_linkspeedsup;
		mad_dump_linkspeedext;
		mad_dump_linkspeedexten;
		mad_dump_linkspeedextsup;
		mad_dump_linkwidth;
		mad_dump_linkwidthen;
		mad_dump_linkwidthsup;
		mad_dump_mlnx_ext_port_info;
		mad_dump_portinfo_ext;
		mad_dump_mtu;
		mad_dump_node_type;
		mad_dump_nodedesc;
		mad_dump_nodeinfo;
		mad_dump_opervls;
		mad_dump_fields;
		mad_dump_perfcounters;
		mad_dump_perfcounters_ext;
		mad_dump_perfcounters_xmt_sl;
		mad_dump_perfcounters_rcv_sl;
		mad_dump_perfcounters_xmt_disc;
		mad_dump_perfcounters_rcv_err;
		mad_dump_physportstate;
		mad_dump_portcapmask;
		mad_dump_portcapmask2;
		mad_dump_portinfo;
		mad_dump_portsamples_control;
		mad_dump_portsamples_result;
		mad_dump_perfcounters_port_op_rcv_counters;
		mad_dump_perfcounters_port_flow_ctl_counters;
		mad_dump_perfcounters_port_vl_op_packet;
		mad_dump_perfcounters_port_vl_op_data;
		mad_dump_perfcounters_port_vl_xmit_flow_ctl_update_errors;
		mad_dump_perfcounters_port_vl_xmit_wait_counters;
		mad_dump_perfcounters_sw_port_vl_congestion;
		mad_dump_perfcounters_rcv_con_ctrl;
		mad_dump_perfcounters_sl_rcv_fecn;
		mad_dump_perfcounters_sl_rcv_becn;
		mad_dump_perfcounters_xmit_con_ctrl;
		mad_dump_perfcounters_vl_xmit_time_cong;
		mad_dump_cc_congestioninfo;
		mad_dump_cc_congestionkeyinfo;
		mad_dump_cc_congestionlog;
		mad_dump_cc_congestionlogswitch;
		mad_dump_cc_congestionlogentryswitch;
		mad_dump_cc_congestionlogca;
		mad_dump_cc_congestionlogentryca;
		mad_dump_cc_switchcongestionsetting;
		mad_dump_cc_switchportcongestionsettingelement;
		mad_dump_cc_cacongestionsetting;
		mad_dump_cc_cacongestionentry;
		mad_dump_cc_congestioncontroltable;
		mad_dump_cc_congestioncontroltableentry;
		mad_dump_cc_timestamp;
		mad_dump_classportinfo;
		mad_dump_portstates;
		mad_dump_portstate;
		mad_dump_rhex;
		mad_dump_sltovl;
		mad_dump_string;
		mad_dump_switchinfo;
		mad_dump_uint;
		mad_dump_vlarbitration;
		mad_dump_vlcap;
		mad_get_field;
		mad_set_field;
		mad_get_field64;
		mad_set_field64;
		mad_get_array;
		mad_set_array;
		pma_query_via;
		performance_reset_via;
		mad_build_pkt;
		mad_decode_field;
		mad_encode;
		mad_encode_field;
		mad_trid;
		portid2portnum;
		portid2str;
		str2drpath;
		drpath2str;
		mad_class_agent;
		mad_register_client;
		mad_register_server;
		mad_register_client_via;
		mad_register_server_via;
		ib_resolve_portid_str;
		ib_resolve_self;
		ib_resolve_smlid;
		ibdebug;
		mad_rpc_open_port;
		mad_rpc_close_port;
		mad_rpc;
		mad_rpc_rmpp;
		mad_rpc_portid;
		mad_rpc_class_agent;
		mad_rpc_set_retries;
		mad_rpc_set_timeout;
		mad_get_timeout;
		mad_get_retries;
		madrpc;
		madrpc_init;
		madrpc_portid;
		madrpc_rmpp;
		madrpc_save_mad;
		madrpc_set_retries;
		madrpc_set_timeout;
		madrpc_show_errors;
		ib_path_query;
		sa_call;
		sa_rpc_call;
		mad_alloc;
		mad_free;
		mad_receive;
		mad_respond;
		mad_receive_via;
		mad_respond_via;
		mad_send;
		mad_send_via;
		smp_query;
		smp_set;
		ib_vendor_call;
		ib_vendor_call_via;
		smp_query_via;
		smp_query_status_via;
		smp_set_via;
		smp_set_status_via;
		ib_path_query_via;
		ib_resolve_smlid_via;
		ib_resolve_guid_via;
		ib_resolve_gid_via;
		ib_resolve_portid_str_via;
		ib_resolve_self_via;
		mad_field_name;
		bm_call_via;
		mad_dump_port_ext_speeds_counters;
		mad_dump_port_ext_speeds_counters_rsfec_active;
		cc_query_status_via;
		cc_config_status_via;
		smp_mkey_get;
		smp_mkey_set;
		ib_node_query_via;
	local: *;
};
