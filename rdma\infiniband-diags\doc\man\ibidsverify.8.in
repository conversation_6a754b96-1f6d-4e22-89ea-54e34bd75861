.\" Man page generated from reStructuredText.
.
.TH IBIDSVERIFY 8 "@BUILD_DATE@" "" "Open IB Diagnostics"
.SH NAME
ibidsverify \- validate IB identifiers in subnet and report errors
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
ibidsverify.pl [\-h] [\-R]
.SH DESCRIPTION
.sp
ibidsverify.pl is a perl script which uses a full topology file that was
created by ibnetdiscover, scans the network to validate the LIDs and GUIDs
in the subnet. The validation consists of checking that there are no zero
or duplicate identifiers.
.sp
Finally, ibidsverify.pl will also reuse the cached ibnetdiscover output from
some of the other diag tools which makes it a bit faster than running
ibnetdiscover from scratch.
.SH OPTIONS
.sp
\fB\-R\fP
Recalculate the ibnetdiscover information, ie do not use the cached
information.  This option is slower but should be used if the diag tools have
not been used for some time or if there are other reasons to believe the
fabric has changed.
.sp
\fB\-C <ca_name>\fP    use the specified ca_name.
.sp
\fB\-P <ca_port>\fP    use the specified ca_port.
.SH EXIT STATUS
.sp
Exit status is 1 if errors are found, 0 otherwise.
.SH FILES
.\" Common text for the config file
.
.SS CONFIG FILE
.sp
@IBDIAG_CONFIG_PATH@/ibdiag.conf
.sp
A global config file is provided to set some of the common options for all
tools.  See supplied config file for details.
.SH SEE ALSO
.sp
\fBibnetdiscover(8)\fP
.SH AUTHOR
.INDENT 0.0
.TP
.B Hal Rosenstock
< \fI\%<EMAIL>\fP >
.UNINDENT
.\" Generated by docutils manpage writer.
.
